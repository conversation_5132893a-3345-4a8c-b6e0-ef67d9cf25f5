/**app.wxss**/

/* 全局样式 */
page {
  background-color: #f7f8fa;
  font-family: -apple-system, BlinkMacSystemFont, 'Helvetica Neue', Helvetica, Segoe UI, Arial, Roboto, 'PingFang SC', 'mi<PERSON>', 'Hiragino Sans GB', 'Microsoft Yahei', sans-serif;
}

/* 容器样式 */
.container {
  padding: 20rpx;
  min-height: 100vh;
}

/* 卡片样式 */
.card {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(100, 101, 102, 0.08);
}

/* 标题样式 */
.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16rpx;
}

.subtitle {
  font-size: 28rpx;
  font-weight: 500;
  color: #646566;
  margin-bottom: 12rpx;
}

/* 文本样式 */
.text-primary {
  color: #1989fa;
}

.text-success {
  color: #07c160;
}

.text-warning {
  color: #ff976a;
}

.text-danger {
  color: #ee0a24;
}

.text-gray {
  color: #969799;
}

.text-dark {
  color: #323233;
}

/* 布局样式 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-center {
  justify-content: center;
  align-items: center;
}

.flex-between {
  justify-content: space-between;
  align-items: center;
}

.flex-around {
  justify-content: space-around;
  align-items: center;
}

.flex-1 {
  flex: 1;
}

/* 间距样式 */
.mt-8 { margin-top: 16rpx; }
.mt-12 { margin-top: 24rpx; }
.mt-16 { margin-top: 32rpx; }
.mt-20 { margin-top: 40rpx; }

.mb-8 { margin-bottom: 16rpx; }
.mb-12 { margin-bottom: 24rpx; }
.mb-16 { margin-bottom: 32rpx; }
.mb-20 { margin-bottom: 40rpx; }

.ml-8 { margin-left: 16rpx; }
.ml-12 { margin-left: 24rpx; }
.ml-16 { margin-left: 32rpx; }

.mr-8 { margin-right: 16rpx; }
.mr-12 { margin-right: 24rpx; }
.mr-16 { margin-right: 32rpx; }

.p-8 { padding: 16rpx; }
.p-12 { padding: 24rpx; }
.p-16 { padding: 32rpx; }
.p-20 { padding: 40rpx; }

/* 按钮样式 */
.btn-full {
  width: 100%;
  margin-top: 32rpx;
}

/* 列表样式 */
.list-item {
  background: #fff;
  padding: 24rpx 32rpx;
  border-bottom: 1rpx solid #ebedf0;
}

.list-item:last-child {
  border-bottom: none;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  color: #969799;
}

.empty-state .icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
}

.empty-state .text {
  font-size: 28rpx;
}
