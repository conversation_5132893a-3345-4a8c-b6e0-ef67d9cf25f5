const Router = require('koa-router');
const { response } = require('../utils/response');
const { validation } = require('../utils/validation');
const { query } = require('../config/database');
const { authMiddleware, permissionMiddleware } = require('../middleware/auth');

const router = new Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取题目列表
router.get('/', permissionMiddleware('question:list'), async (ctx) => {
  const { page = 1, pageSize = 10, title, chapterId, categoryId, type, difficulty, status } = ctx.query;

  let whereClause = 'WHERE 1=1';
  const params = [];

  if (title && title !== '') {
    whereClause += ' AND q.title LIKE ?';
    params.push(`%${title}%`);
  }

  if (chapterId && chapterId !== '') {
    whereClause += ' AND q.chapter_id = ?';
    params.push(chapterId);
  }

  if (categoryId && categoryId !== '') {
    whereClause += ' AND q.category_id = ?';
    params.push(categoryId);
  }

  if (type && type !== '') {
    whereClause += ' AND q.type = ?';
    params.push(type);
  }

  if (difficulty && difficulty !== '') {
    whereClause += ' AND q.difficulty = ?';
    params.push(difficulty);
  }

  if (status !== undefined && status !== '') {
    whereClause += ' AND q.status = ?';
    params.push(status);
  }

  // 查询总数
  const countSql = `SELECT COUNT(*) as total FROM questions q ${whereClause}`;
  const countResult = await query(countSql, params);
  const total = countResult[0].total;

  // 查询数据
  const offset = (page - 1) * pageSize;
  const dataSql = `
    SELECT q.id, q.title, q.type, q.difficulty, q.chapter_id, q.category_id, q.status, q.created_time,
           c.name as chapter_name, cat.name as category_name
    FROM questions q
    LEFT JOIN chapters c ON q.chapter_id = c.id
    LEFT JOIN question_categories cat ON q.category_id = cat.id
    ${whereClause}
    ORDER BY q.created_time DESC
    LIMIT ${parseInt(pageSize)} OFFSET ${parseInt(offset)}
  `;

  const questions = await query(dataSql, params);

  ctx.body = response.page(questions, total, page, pageSize);
});

// 创建题目
router.post('/', permissionMiddleware('question:list'), async (ctx) => {
  const { title, content, type, difficulty, chapterId, categoryId, options, answer, explanation } = ctx.request.body;

  // 参数验证
  validation.required(title, '题目标题');
  validation.required(content, '题目内容');
  validation.required(type, '题目类型');
  validation.required(answer, '题目答案');

  // 创建题目
  const result = await query(
    'INSERT INTO questions (title, content, type, difficulty, chapter_id, category_id, options, answer, explanation, status, created_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
    [title, content, type, difficulty || 1, chapterId || null, categoryId || null, JSON.stringify(options || []), answer, explanation || null, 1, new Date()]
  );

  ctx.body = response.success({ id: result.insertId, title }, '题目创建成功');
});

// 更新题目
router.put('/:id', permissionMiddleware('question:list'), async (ctx) => {
  const { id } = ctx.params;
  const { title, content, type, difficulty, chapterId, categoryId, options, answer, explanation, status } = ctx.request.body;

  // 参数验证
  validation.required(title, '题目标题');
  validation.required(content, '题目内容');
  validation.required(type, '题目类型');
  validation.required(answer, '题目答案');

  // 检查题目是否存在
  const existingQuestions = await query('SELECT id FROM questions WHERE id = ?', [id]);

  if (existingQuestions.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('题目不存在', 404);
    return;
  }

  // 更新题目
  await query(
    'UPDATE questions SET title = ?, content = ?, type = ?, difficulty = ?, chapter_id = ?, category_id = ?, options = ?, answer = ?, explanation = ?, status = ?, updated_time = ? WHERE id = ?',
    [title, content, type, difficulty || 1, chapterId || null, categoryId || null, JSON.stringify(options || []), answer, explanation || null, status, new Date(), id]
  );

  ctx.body = response.success(null, '题目更新成功');
});

// 删除题目
router.delete('/:id', permissionMiddleware('question:list'), async (ctx) => {
  const { id } = ctx.params;

  // 检查题目是否存在
  const existingQuestions = await query('SELECT id FROM questions WHERE id = ?', [id]);

  if (existingQuestions.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('题目不存在', 404);
    return;
  }

  // 删除题目
  await query('DELETE FROM questions WHERE id = ?', [id]);

  ctx.body = response.success(null, '题目删除成功');
});

module.exports = router;
