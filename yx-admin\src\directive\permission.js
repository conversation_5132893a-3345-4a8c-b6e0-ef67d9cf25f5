import store from '@/store'

/**
 * 权限指令
 * 使用方法：v-permission="'system:user:add'"
 * 或者：v-permission="['system:user:add', 'system:user:edit']"
 */
export default {
  inserted(el, binding) {
    const { value } = binding
    const permissions = store.getters['user/userPermissions'] || []

    if (value) {
      let hasPermission = false

      if (Array.isArray(value)) {
        // 数组形式，只要有一个权限就显示
        hasPermission = value.some(permission => permissions.includes(permission))
      } else {
        // 字符串形式
        hasPermission = permissions.includes(value)
      }

      if (!hasPermission) {
        el.parentNode && el.parentNode.removeChild(el)
      }
    }
  }
}
