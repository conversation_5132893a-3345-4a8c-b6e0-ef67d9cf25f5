/* pages/question-banks/question-banks.wxss */
.container {
  padding: 24rpx;
}

/* 题库列表 */
.banks-list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.bank-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 32rpx;
  border-bottom: 1rpx solid #ebedf0;
  transition: background-color 0.2s;
}

.bank-item:last-child {
  border-bottom: none;
}

.bank-item:active {
  background-color: #f7f8fa;
}

.bank-info {
  flex: 1;
  margin-right: 16rpx;
}

.bank-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 8rpx;
}

.bank-desc {
  font-size: 28rpx;
  color: #969799;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.bank-stats {
  display: flex;
  gap: 16rpx;
}

/* 标签样式 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 0 12rpx;
  font-size: 20rpx;
  line-height: 32rpx;
  border-radius: 4rpx;
}

.tag-primary {
  color: #1989fa;
  background-color: #ecf5ff;
}

.tag-success {
  color: #07c160;
  background-color: #f0fff0;
}

/* 图标样式 */
.icon-arrow {
  width: 32rpx;
  height: 32rpx;
}

.empty-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
}

/* 加载样式 */
.loading-spinner {
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid #c8c9cc;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #969799;
  margin-top: 24rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #969799;
  margin-top: 16rpx;
}
