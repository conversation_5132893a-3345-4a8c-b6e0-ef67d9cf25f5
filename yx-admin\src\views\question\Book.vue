<template>
  <div class="book-management">
    <el-card>
      <!-- 面包屑导航 -->
      <div class="breadcrumb-area" v-if="currentBank">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/question/bank' }">题库管理</el-breadcrumb-item>
          <el-breadcrumb-item>{{ currentBank }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="书籍名称">
            <el-input v-model="searchForm.name" placeholder="请输入书籍名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="年级">
            <el-select v-model="searchForm.grade" placeholder="请选择年级" clearable>
              <el-option label="一年级" value="1"></el-option>
              <el-option label="二年级" value="2"></el-option>
              <el-option label="三年级" value="3"></el-option>
              <el-option label="四年级" value="4"></el-option>
              <el-option label="五年级" value="5"></el-option>
              <el-option label="六年级" value="6"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="启用" value="1"></el-option>
              <el-option label="禁用" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleAdd">新增书籍</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="书籍名称"></el-table-column>
        <el-table-column prop="grade" label="年级" width="100">
          <template slot-scope="scope">
            <el-tag type="primary">{{ scope.row.grade }}年级</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="version" label="版本" width="120"></el-table-column>
        <el-table-column prop="chapterCount" label="章节数量" width="100">
          <template slot-scope="scope">
            <el-tag type="info">{{ scope.row.chapterCount }}章</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="questionCount" label="题目数量" width="100">
          <template slot-scope="scope">
            <el-tag type="success">{{ scope.row.questionCount }}题</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="80"></el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160"></el-table-column>
        <el-table-column label="操作" width="250">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="primary" @click="handleChapters(scope.row)">章节</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        style="margin-top: 20px; text-align: right;">
      </el-pagination>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="书籍名称" prop="name">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="年级" prop="grade">
          <el-select v-model="form.grade" placeholder="请选择年级" style="width: 100%;">
            <el-option label="一年级" value="1"></el-option>
            <el-option label="二年级" value="2"></el-option>
            <el-option label="三年级" value="3"></el-option>
            <el-option label="四年级" value="4"></el-option>
            <el-option label="五年级" value="5"></el-option>
            <el-option label="六年级" value="6"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="版本" prop="version">
          <el-input v-model="form.version" placeholder="如：人教版、苏教版等"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" rows="3"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Book',
  data() {
    return {
      loading: false,
      currentBank: '',
      bankId: null,
      searchForm: {
        name: '',
        grade: '',
        status: ''
      },
      tableData: [
        {
          id: 1,
          name: '小学数学一年级上册',
          grade: '1',
          version: '人教版',
          chapterCount: 8,
          questionCount: 120,
          sort: 1,
          status: 1,
          createTime: '2024-01-01 10:00:00'
        },
        {
          id: 2,
          name: '小学数学一年级下册',
          grade: '1',
          version: '人教版',
          chapterCount: 6,
          questionCount: 95,
          sort: 2,
          status: 1,
          createTime: '2024-01-01 10:00:00'
        },
        {
          id: 3,
          name: '小学数学二年级上册',
          grade: '2',
          version: '人教版',
          chapterCount: 9,
          questionCount: 150,
          sort: 3,
          status: 1,
          createTime: '2024-01-01 10:00:00'
        }
      ],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 3
      },
      dialogVisible: false,
      isEdit: false,
      form: {
        id: null,
        name: '',
        grade: '',
        version: '',
        description: '',
        sort: 0,
        status: 1
      },
      rules: {
        name: [
          { required: true, message: '请输入书籍名称', trigger: 'blur' }
        ],
        grade: [
          { required: true, message: '请选择年级', trigger: 'change' }
        ],
        version: [
          { required: true, message: '请输入版本', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑书籍' : '新增书籍'
    }
  },
  methods: {
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadData()
    },
    handleReset() {
      this.searchForm = {
        name: '',
        grade: '',
        status: ''
      }
      this.loadData()
    },
    handleAdd() {
      this.isEdit = false
      this.form = {
        id: null,
        name: '',
        grade: '',
        version: '',
        description: '',
        sort: 0,
        status: 1
      }
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.isEdit = true
      this.form = { ...row }
      this.dialogVisible = true
    },
    handleChapters(row) {
      this.$router.push({
        path: '/question/chapter',
        query: { 
          bankId: this.bankId,
          bankName: this.currentBank,
          bookId: row.id, 
          bookName: row.name 
        }
      })
    },
    handleDelete(row) {
      this.$confirm('确定要删除该书籍吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
        this.loadData()
      })
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$message.success(this.isEdit ? '编辑成功' : '新增成功')
          this.dialogVisible = false
          this.loadData()
        }
      })
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.loadData()
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.loadData()
    },
    loadData() {
      this.loading = true
      // 模拟API调用
      setTimeout(() => {
        this.loading = false
      }, 500)
    }
  },
  mounted() {
    // 获取路由参数
    this.bankId = this.$route.query.bankId
    this.currentBank = this.$route.query.bankName
    this.loadData()
  }
}
</script>

<style lang="scss" scoped>
.book-management {
  .breadcrumb-area {
    margin-bottom: 20px;
  }
  
  .search-area {
    margin-bottom: 20px;
    
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
}
</style>
