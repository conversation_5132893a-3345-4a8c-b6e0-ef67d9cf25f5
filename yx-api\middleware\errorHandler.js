const { response } = require('../utils/response');

const errorHandler = async (ctx, next) => {
  try {
    await next();
  } catch (error) {
    console.error('Error:', error);
    
    // 根据错误类型返回不同的状态码和消息
    let status = 500;
    let message = '服务器内部错误';
    
    if (error.name === 'ValidationError') {
      status = 400;
      message = error.message;
    } else if (error.name === 'UnauthorizedError') {
      status = 401;
      message = '未授权访问';
    } else if (error.name === 'ForbiddenError') {
      status = 403;
      message = '权限不足';
    } else if (error.name === 'NotFoundError') {
      status = 404;
      message = '资源不存在';
    } else if (error.code === 'ER_DUP_ENTRY') {
      status = 400;
      message = '数据已存在';
    } else if (error.code === 'ER_NO_REFERENCED_ROW_2') {
      status = 400;
      message = '关联数据不存在';
    }
    
    ctx.status = status;
    ctx.body = response.error(message, status);
    
    // 记录错误日志
    if (status >= 500) {
      console.error(`[${new Date().toISOString()}] ${ctx.method} ${ctx.url} - ${status} - ${error.stack}`);
    }
  }
};

module.exports = {
  errorHandler
};
