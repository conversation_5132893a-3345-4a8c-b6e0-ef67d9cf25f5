import { constantRoutes, asyncRoutes } from '@/router'
import Layout from '@/layout'

/**
 * 通过meta.role判断是否与当前用户权限匹配
 * @param roles
 * @param route
 */
function hasPermission(permissions, route) {
  if (route.meta && route.meta.permission) {
    return permissions.includes(route.meta.permission)
  } else {
    return true
  }
}

/**
 * 递归过滤异步路由表，返回符合用户角色权限的路由表
 * @param routes asyncRoutes
 * @param permissions
 */
function filterAsyncRoutes(routes, permissions) {
  const res = []

  routes.forEach(route => {
    const tmp = { ...route }
    if (hasPermission(permissions, tmp)) {
      if (tmp.children) {
        tmp.children = filterAsyncRoutes(tmp.children, permissions)
      }
      res.push(tmp)
    }
  })

  return res
}

/**
 * 将后端菜单数据转换为前端路由格式
 * @param {Array} menus 后端菜单数据
 */
function generateRoutes(menus) {
  const routes = []

  menus.forEach(menu => {
    const route = {
      path: menu.path,
      name: menu.name,
      component: menu.component === 'Layout' ? Layout : loadView(menu.component),
      meta: {
        title: menu.name,
        icon: menu.icon,
        permission: menu.permission
      }
    }

    if (menu.children && menu.children.length > 0) {
      route.children = generateRoutes(menu.children)
    }

    routes.push(route)
  })

  return routes
}

/**
 * 动态加载组件
 * @param {String} view 组件路径
 */
function loadView(view) {
  if (!view) return null

  // 处理特殊组件
  if (view === 'Layout') {
    return Layout
  }

  // 动态导入组件
  return () => import(`@/views/${view}.vue`)
}

const state = {
  routes: [],
  addRoutes: []
}

const mutations = {
  SET_ROUTES: (state, routes) => {
    state.addRoutes = routes
    state.routes = constantRoutes.concat(routes)
  },
  RESET_ROUTES: (state) => {
    state.addRoutes = []
    state.routes = constantRoutes
  }
}

const getters = {
  routes: state => state.routes,
  addRoutes: state => state.addRoutes
}

const actions = {
  generateRoutes({ commit }, { menus, permissions }) {
    return new Promise(resolve => {
      let accessedRoutes

      if (menus && menus.length > 0) {
        // 根据后端菜单数据生成路由
        accessedRoutes = generateRoutes(menus)
      } else {
        // 如果没有菜单数据，使用权限过滤异步路由
        accessedRoutes = filterAsyncRoutes(asyncRoutes, permissions)
      }

      commit('SET_ROUTES', accessedRoutes)
      resolve(accessedRoutes)
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions,
  getters
}
