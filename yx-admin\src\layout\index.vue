<template>
  <div class="app-wrapper">
    <el-container>
      <!-- 侧边栏 -->
      <el-aside :width="isCollapse ? '64px' : '200px'" class="sidebar-container">
        <div class="logo">
          <h3 v-if="!isCollapse">YX管理系统</h3>
          <h3 v-else>YX</h3>
        </div>
        <el-menu
          :default-active="$route.path"
          :collapse="isCollapse"
          background-color="#304156"
          text-color="#bfcbd9"
          active-text-color="#409EFF"
          router
        >
          <template v-for="route in menuRoutes">
            <el-submenu v-if="route.children && route.children.length > 1" :index="route.path" :key="route.path">
              <template slot="title">
                <i :class="route.meta.icon"></i>
                <span>{{ route.meta.title }}</span>
              </template>
              <el-menu-item
                v-for="child in route.children"
                :key="child.path"
                :index="route.path + '/' + child.path"
              >
                <i :class="child.meta.icon"></i>
                <span>{{ child.meta.title }}</span>
              </el-menu-item>
            </el-submenu>
            <el-menu-item v-else-if="route.children && route.children.length === 1" :index="route.children[0].path" :key="route.path">
              <i :class="route.children[0].meta.icon"></i>
              <span>{{ route.children[0].meta.title }}</span>
            </el-menu-item>
          </template>
        </el-menu>
      </el-aside>

      <el-container>
        <!-- 头部 -->
        <el-header class="navbar">
          <div class="left">
            <el-button type="text" @click="toggleSidebar">
              <i class="el-icon-s-fold" v-if="!isCollapse"></i>
              <i class="el-icon-s-unfold" v-else></i>
            </el-button>
          </div>
          <div class="right">
            <el-dropdown @command="handleCommand">
              <span class="el-dropdown-link">
                {{ userInfo.name || '管理员' }}
                <i class="el-icon-arrow-down el-icon--right"></i>
              </span>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item command="logout">退出登录</el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </el-header>

        <!-- 主内容区 -->
        <el-main class="app-main">
          <router-view />
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script>
import { mapGetters, mapActions } from 'vuex'

export default {
  name: 'Layout',
  data() {
    return {
      isCollapse: false
    }
  },
  computed: {
    ...mapGetters('user', ['userInfo', 'userPermissions']),
    menuRoutes() {
      const routes = this.filterRoutes(this.$router.options.routes)
      console.log('菜单路由:', routes)
      return routes
    }
  },
  methods: {
    ...mapActions('user', ['logout']),
    async handleLogout() {
      await this.logout()
      this.$router.push('/login')
    },
    filterRoutes(routes) {
      return routes.filter(route => {
        if (route.hidden) return false

        // 暂时跳过权限检查，让管理员看到所有菜单
        // TODO: 后续完善权限系统后再启用
        // if (route.meta && route.meta.permission) {
        //   if (!this.userPermissions.includes(route.meta.permission)) {
        //     return false
        //   }
        // }

        // 递归过滤子路由
        if (route.children) {
          route.children = this.filterRoutes(route.children)
        }

        return true
      })
    },
    toggleSidebar() {
      this.isCollapse = !this.isCollapse
    },
    handleCommand(command) {
      if (command === 'logout') {
        this.$confirm('确定要退出登录吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$store.dispatch('user/logout')
          this.$router.push('/login')
          this.$message.success('退出成功')
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.app-wrapper {
  height: 100vh;
}

.sidebar-container {
  background-color: #304156;
  transition: width 0.28s;
  height: 100vh;

  .logo {
    height: 50px;
    line-height: 50px;
    text-align: center;
    background-color: #2b2f3a;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
  }

  .el-menu {
    border-right: none;
    height: calc(100vh - 50px);
    overflow-y: auto;
  }
}

.navbar {
  background-color: #fff;
  box-shadow: 0 1px 4px rgba(0,21,41,.08);
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20px;
  height: 60px;

  .left {
    .el-button {
      font-size: 18px;
    }
  }

  .right {
    .el-dropdown-link {
      cursor: pointer;
      color: #606266;
    }
  }
}

.app-main {
  background-color: #f0f2f5;
  padding: 20px;
  height: calc(100vh - 60px);
  overflow-y: auto;
  box-sizing: border-box;
}
</style>
