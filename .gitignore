# YX刷题项目 - 根目录 .gitignore 文件
# 整合三个子项目的忽略规则

# ===== 通用忽略规则 =====

# 操作系统生成的文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# IDE 和编辑器文件
.vscode/
.idea/
*.swp
*.swo
*~

# 日志文件
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# 运行时数据
pids
*.pid
*.seed
*.pid.lock

# 覆盖率目录
lib-cov
coverage/
*.lcov
.nyc_output

# 依赖目录
node_modules/
jspm_packages/

# 可选的 npm 缓存目录
.npm

# 可选的 eslint 缓存
.eslintcache

# 环境变量文件
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# ===== yx-admin 项目忽略规则 =====

# Vue.js 构建输出
yx-admin/dist/
yx-admin/.tmp/
yx-admin/.cache/

# Vue.js 依赖
yx-admin/node_modules/

# Vue.js 环境变量
yx-admin/.env
yx-admin/.env.local
yx-admin/.env.*.local

# Vue.js 编辑器目录
yx-admin/.vscode/
yx-admin/.idea/

# Vue.js 日志
yx-admin/npm-debug.log*
yx-admin/yarn-debug.log*
yx-admin/yarn-error.log*

# Vue.js 测试覆盖率
yx-admin/coverage/
yx-admin/tests/e2e/videos/
yx-admin/tests/e2e/screenshots/

# ===== yx-api 项目忽略规则 =====

# Node.js 依赖
yx-api/node_modules/

# Node.js 环境变量
yx-api/.env
yx-api/.env.local
yx-api/.env.*.local

# Node.js 日志
yx-api/logs/
yx-api/*.log
yx-api/npm-debug.log*
yx-api/yarn-debug.log*
yx-api/yarn-error.log*

# Node.js 运行时
yx-api/pids/
yx-api/*.pid
yx-api/*.seed
yx-api/*.pid.lock

# Node.js 覆盖率
yx-api/coverage/
yx-api/.nyc_output/

# Node.js 缓存
yx-api/.npm/
yx-api/.eslintcache

# PM2 配置
yx-api/ecosystem.config.js

# 数据库备份文件
yx-api/database/backup/
yx-api/*.sql.backup

# 上传文件目录
yx-api/uploads/
yx-api/public/uploads/

# ===== yx-wechat 项目忽略规则 =====

# 微信小程序依赖
yx-wechat/node_modules/
yx-wechat/miniprogram_npm/

# 微信小程序构建文件
yx-wechat/dist/
yx-wechat/.tmp/

# 微信小程序日志
yx-wechat/npm-debug.log*
yx-wechat/yarn-debug.log*
yx-wechat/yarn-error.log*

# 微信小程序缓存
yx-wechat/.eslintcache

# 微信开发者工具生成的文件
yx-wechat/project.private.config.json

# ===== 其他忽略规则 =====

# 压缩文件
*.zip
*.tar.gz
*.rar

# 临时文件
*.tmp
*.temp

# 备份文件
*.bak
*.backup

# 文档生成目录
docs/build/
docs/.vuepress/dist/

# 测试文件
test-results/
playwright-report/

# 部署相关
deploy/
.deploy/

# 证书文件
*.pem
*.key
*.crt

# 配置文件（包含敏感信息）
config/production.json
config/local.json

# 数据文件
*.db
*.sqlite
*.sqlite3

# Redis 数据文件
dump.rdb

# 监控和分析
.monitoring/
.analytics/

# Docker 相关
.dockerignore
docker-compose.override.yml

# Kubernetes 相关
k8s/secrets/
*.secret.yaml

# 版本控制
.git/
.gitattributes

# 包管理器锁文件（可选择性忽略）
# package-lock.json
# yarn.lock

# 编译输出
build/
dist/
out/

# 缓存目录
.cache/
.parcel-cache/
.next/
.nuxt/

# 静态文件生成
_site/
.jekyll-cache/

# 错误报告
error-report.html
error-report.json

# 性能分析
profile/
*.cpuprofile
*.heapprofile

# 安全扫描报告
security-report.html
security-report.json

# API 文档生成
api-docs/
swagger-ui/

# 本地开发配置
.local/
local.config.js

# 第三方服务配置
.firebase/
.vercel/
.netlify/

# 移动端相关
platforms/
plugins/
www/

# 桌面应用相关
electron-dist/
release/

# 国际化文件
locales/build/
i18n/build/

# 字体文件（如果很大）
# fonts/
# *.woff
# *.woff2
# *.ttf
# *.otf

# 图片文件（如果很大）
# images/original/
# *.psd
# *.ai
# *.sketch

# 视频文件
# videos/
# *.mp4
# *.avi
# *.mov

# 音频文件
# audio/
# *.mp3
# *.wav
# *.flac
