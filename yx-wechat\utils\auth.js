// utils/auth.js
const app = getApp()

// 检查登录状态
function checkLogin() {
  return app.isLoggedIn()
}

// 要求登录
function requireLogin() {
  if (!checkLogin()) {
    wx.showModal({
      title: '提示',
      content: '请先登录',
      showCancel: false,
      success: () => {
        wx.navigateTo({
          url: '/pages/login/login'
        })
      }
    })
    return false
  }
  return true
}

// 登录
function login(username, password) {
  return new Promise((resolve, reject) => {
    const api = require('./api.js')
    
    api.auth.login({ username, password })
      .then(res => {
        const { token, user } = res.data
        app.setUserInfo(user, token)
        resolve(res)
      })
      .catch(err => {
        reject(err)
      })
  })
}

// 登出
function logout() {
  return new Promise((resolve) => {
    wx.showModal({
      title: '确认',
      content: '确定要退出登录吗？',
      success: (res) => {
        if (res.confirm) {
          app.clearUserInfo()
          wx.reLaunch({
            url: '/pages/login/login'
          })
          resolve()
        }
      }
    })
  })
}

// 获取用户信息
function getUserInfo() {
  return app.globalData.userInfo
}

module.exports = {
  checkLogin,
  requireLogin,
  login,
  logout,
  getUserInfo
}
