// pages/questions/questions.js
const auth = require('../../utils/auth.js')
const api = require('../../utils/api.js')
const storage = require('../../utils/storage.js')
const Toast = require('../../utils/toast.js')
const Dialog = require('../../utils/dialog.js')

Page({
  data: {
    subjectId: '',
    subjectName: '',
    bankId: '',
    bankName: '',
    bookId: '',
    bookName: '',
    chapterId: '',
    chapterName: '',
    questions: [],
    currentIndex: 0,
    currentQuestion: null,
    selectedAnswer: '',
    selectedAnswers: [],
    fillAnswer: '',
    essayAnswer: '',
    showAnalysis: false,
    loading: false,
    answeredQuestions: []
  },

  onLoad(options) {
    const { 
      subjectId, subjectName, bankId, bankName, 
      bookId, bookName, chapterId, chapterName 
    } = options
    
    if (!subjectId || !bankId || !bookId || !chapterId) {
      Toast.fail('参数错误')
      wx.navigateBack()
      return
    }

    this.setData({
      subjectId,
      subjectName: decodeURIComponent(subjectName || ''),
      bankId,
      bankName: decodeURIComponent(bankName || ''),
      bookId,
      bookName: decodeURIComponent(bookName || ''),
      chapterId,
      chapterName: decodeURIComponent(chapterName || '')
    })

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: this.data.chapterName || '练习'
    })

    this.checkLoginAndLoadData()
  },

  // 检查登录状态并加载数据
  checkLoginAndLoadData() {
    if (!auth.requireLogin()) return
    this.loadQuestions()
  },

  // 加载题目列表
  loadQuestions() {
    this.setData({ loading: true })
    
    api.questions.list({ chapterId: this.data.chapterId })
      .then(res => {
        const questions = res.data?.list || []
        if (questions.length > 0) {
          this.setData({
            questions,
            currentQuestion: questions[0],
            answeredQuestions: new Array(questions.length).fill(null)
          })
        }
      })
      .catch(err => {
        console.error('加载题目失败:', err)
        Toast.fail('加载题目失败')
      })
      .finally(() => {
        this.setData({ loading: false })
      })
  },

  // 单选题选择
  onSingleChoiceChange(event) {
    this.setData({ selectedAnswer: event.detail.value })
  },

  // 多选题选择
  onMultipleChoiceChange(event) {
    this.setData({ selectedAnswers: event.detail.value })
  },

  // 填空题输入
  onFillAnswerChange(event) {
    this.setData({ fillAnswer: event.detail.value })
  },

  // 简答题输入
  onEssayAnswerChange(event) {
    this.setData({ essayAnswer: event.detail.value })
  },

  // 检查是否有答案
  hasAnswer() {
    const { currentQuestion, selectedAnswer, selectedAnswers, fillAnswer, essayAnswer } = this.data
    
    if (!currentQuestion) return false
    
    switch (currentQuestion.type) {
      case 'single':
      case 'judge':
        return !!selectedAnswer
      case 'multiple':
        return selectedAnswers.length > 0
      case 'fill':
        return !!fillAnswer.trim()
      case 'essay':
        return !!essayAnswer.trim()
      default:
        return false
    }
  },

  // 获取当前答案
  getCurrentAnswer() {
    const { currentQuestion, selectedAnswer, selectedAnswers, fillAnswer, essayAnswer } = this.data
    
    switch (currentQuestion.type) {
      case 'single':
      case 'judge':
        return selectedAnswer
      case 'multiple':
        return selectedAnswers.join(',')
      case 'fill':
        return fillAnswer.trim()
      case 'essay':
        return essayAnswer.trim()
      default:
        return ''
    }
  },

  // 提交答案
  submitAnswer() {
    const { currentQuestion, currentIndex } = this.data
    const userAnswer = this.getCurrentAnswer()
    const correctAnswer = currentQuestion.answer
    
    // 判断答案是否正确
    let isCorrect = false
    if (currentQuestion.type === 'multiple') {
      const userAnswerArray = userAnswer.split(',').sort()
      const correctAnswerArray = correctAnswer.split(',').sort()
      isCorrect = JSON.stringify(userAnswerArray) === JSON.stringify(correctAnswerArray)
    } else {
      isCorrect = userAnswer.toLowerCase() === correctAnswer.toLowerCase()
    }

    // 保存答题记录
    storage.progress.saveAnswerRecord(currentQuestion.id, userAnswer, isCorrect)
    
    // 更新已答题目
    const answeredQuestions = [...this.data.answeredQuestions]
    answeredQuestions[currentIndex] = {
      userAnswer,
      isCorrect,
      timestamp: Date.now()
    }

    this.setData({
      answeredQuestions,
      showAnalysis: true
    })

    // 显示结果提示
    if (isCorrect) {
      Toast.success('回答正确！')
    } else {
      Toast.fail('回答错误')
    }

    // 更新学习进度
    this.updateProgress()
  },

  // 更新学习进度
  updateProgress() {
    const { subjectId, bankId, bookId, chapterId, questions, answeredQuestions } = this.data
    const completed = answeredQuestions.filter(item => item !== null).length
    const total = questions.length
    const correctCount = answeredQuestions.filter(item => item && item.isCorrect).length
    const correctRate = completed > 0 ? (correctCount / completed * 100) : 0

    storage.progress.saveProgress(subjectId, bankId, bookId, chapterId, {
      completed,
      total,
      correctRate: Math.round(correctRate)
    })
  },

  // 上一题
  prevQuestion() {
    if (this.data.currentIndex > 0) {
      this.switchQuestion(this.data.currentIndex - 1)
    }
  },

  // 下一题
  nextQuestion() {
    if (this.data.currentIndex < this.data.questions.length - 1) {
      this.switchQuestion(this.data.currentIndex + 1)
    }
  },

  // 切换题目
  switchQuestion(index) {
    const { questions } = this.data
    const question = questions[index]
    
    this.setData({
      currentIndex: index,
      currentQuestion: question,
      selectedAnswer: '',
      selectedAnswers: [],
      fillAnswer: '',
      essayAnswer: '',
      showAnalysis: false
    })
  },

  // 完成练习
  finishPractice() {
    const { answeredQuestions, questions } = this.data
    const completed = answeredQuestions.filter(item => item !== null).length
    const correctCount = answeredQuestions.filter(item => item && item.isCorrect).length
    
    Dialog.alert({
      title: '练习完成',
      message: `本次练习完成 ${completed}/${questions.length} 题，正确率 ${completed > 0 ? Math.round(correctCount / completed * 100) : 0}%`,
      confirmButtonText: '确定'
    }).then(() => {
      wx.navigateBack()
    })
  },

  // 获取题目类型文本
  getQuestionTypeText(type) {
    const typeMap = {
      'single': '单选题',
      'multiple': '多选题',
      'judge': '判断题',
      'fill': '填空题',
      'essay': '简答题'
    }
    return typeMap[type] || '未知类型'
  },

  // 获取难度文本
  getDifficultyText(difficulty) {
    const difficultyMap = {
      'easy': '简单',
      'medium': '中等',
      'hard': '困难'
    }
    return difficultyMap[difficulty] || '未知难度'
  },

  // 获取正确答案文本
  getCorrectAnswerText() {
    const { currentQuestion } = this.data
    if (!currentQuestion) return ''
    
    switch (currentQuestion.type) {
      case 'single':
        const singleOption = currentQuestion.options?.find(opt => opt.key === currentQuestion.answer)
        return singleOption ? `${currentQuestion.answer}. ${singleOption.value}` : currentQuestion.answer
      case 'multiple':
        const multipleKeys = currentQuestion.answer.split(',')
        const multipleOptions = currentQuestion.options?.filter(opt => multipleKeys.includes(opt.key))
        return multipleOptions?.map(opt => `${opt.key}. ${opt.value}`).join('；') || currentQuestion.answer
      case 'judge':
        return currentQuestion.answer === 'true' ? '正确' : '错误'
      case 'fill':
      case 'essay':
        return currentQuestion.answer
      default:
        return currentQuestion.answer
    }
  }
})
