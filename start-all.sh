#!/bin/bash

echo "🚀 启动YX刷题小程序系统..."

# 检查Node.js是否安装
if ! command -v node &> /dev/null; then
    echo "❌ Node.js 未安装，请先安装 Node.js"
    exit 1
fi

# 检查npm是否安装
if ! command -v npm &> /dev/null; then
    echo "❌ npm 未安装，请先安装 npm"
    exit 1
fi

echo "📦 安装依赖..."

# 安装后端依赖
echo "安装后端依赖..."
cd yx-api
if [ ! -d "node_modules" ]; then
    npm install
fi

# 复制环境配置文件
if [ ! -f ".env" ]; then
    cp .env.example .env
    echo "✅ 已创建环境配置文件 .env，请根据需要修改数据库配置"
fi

# 初始化数据库
echo "🗄️ 初始化数据库..."
node scripts/initDatabase.js

# 启动后端服务
echo "🔧 启动后端API服务..."
npm run dev &
BACKEND_PID=$!

cd ..

# 安装前端依赖
echo "安装前端依赖..."
cd yx-admin
if [ ! -d "node_modules" ]; then
    npm install
fi

# 启动前端服务
echo "🎨 启动前端管理系统..."
npm run serve &
FRONTEND_PID=$!

cd ..

echo "✅ 系统启动完成！"
echo ""
echo "📋 服务信息："
echo "   后端API服务: http://localhost:3000"
echo "   前端管理系统: http://localhost:8080"
echo ""
echo "🔑 默认登录账号："
echo "   用户名: admin"
echo "   密码: 123456"
echo ""
echo "💡 提示："
echo "   - 按 Ctrl+C 停止所有服务"
echo "   - 微信小程序需要使用微信开发者工具打开 yx-wechat 目录"
echo ""

# 等待用户中断
wait $BACKEND_PID $FRONTEND_PID
