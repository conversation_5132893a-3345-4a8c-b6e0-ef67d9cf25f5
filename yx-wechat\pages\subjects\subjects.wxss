/* pages/subjects/subjects.wxss */
.container {
  padding: 24rpx;
}

/* 搜索栏 */
.search-bar {
  background: #fff;
  border-radius: 16rpx;
  margin-bottom: 24rpx;
  padding: 8rpx;
}

/* 学科列表 */
.subjects-list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.subject-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #ebedf0;
  transition: background-color 0.2s;
}

.subject-item:last-child {
  border-bottom: none;
}

.subject-item:active {
  background-color: #f7f8fa;
}

.subject-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.subject-info {
  flex: 1;
  margin-right: 16rpx;
}

.subject-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 8rpx;
}

.subject-desc {
  font-size: 28rpx;
  color: #969799;
  margin-bottom: 12rpx;
  line-height: 1.4;
}

.subject-stats {
  display: flex;
  gap: 16rpx;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #969799;
  margin-top: 24rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #969799;
  margin-top: 16rpx;
}
