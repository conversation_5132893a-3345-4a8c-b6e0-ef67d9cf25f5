// utils/notify.js
// 替代 Vant Notify 的原生实现

const Notify = {
  // 显示成功通知
  success(options) {
    const message = typeof options === 'string' ? options : options.message
    wx.showToast({
      title: message,
      icon: 'success',
      duration: (options.duration || 3000),
      mask: false
    })
  },

  // 显示错误通知
  error(options) {
    const message = typeof options === 'string' ? options : options.message
    wx.showToast({
      title: message,
      icon: 'error',
      duration: (options.duration || 3000),
      mask: false
    })
  },

  // 显示警告通知
  warning(options) {
    const message = typeof options === 'string' ? options : options.message
    wx.showToast({
      title: message,
      icon: 'none',
      duration: (options.duration || 3000),
      mask: false
    })
  },

  // 显示普通通知
  info(options) {
    const message = typeof options === 'string' ? options : options.message
    wx.showToast({
      title: message,
      icon: 'none',
      duration: (options.duration || 3000),
      mask: false
    })
  }
}

module.exports = Notify
