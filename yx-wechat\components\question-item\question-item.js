// components/question-item/question-item.js
Component({
  properties: {
    // 题目数据
    question: {
      type: Object,
      value: {}
    },
    // 是否显示解析
    showAnalysis: {
      type: Boolean,
      value: false
    },
    // 选中的答案
    selectedAnswer: {
      type: String,
      value: ''
    },
    // 是否禁用选择
    disabled: {
      type: Boolean,
      value: false
    }
  },

  data: {
    
  },

  methods: {
    // 选项选择
    onOptionSelect(e) {
      if (this.data.disabled) return
      
      const key = e.currentTarget.dataset.key
      this.setData({
        selectedAnswer: key
      })
      
      // 触发父组件事件
      this.triggerEvent('select', {
        answer: key,
        question: this.data.question
      })
    },

    // 获取题目类型文本
    getQuestionTypeText(type) {
      const typeMap = {
        'single': '单选题',
        'multiple': '多选题',
        'judge': '判断题',
        'fill': '填空题',
        'essay': '简答题'
      }
      return typeMap[type] || '未知类型'
    },

    // 获取难度文本
    getDifficultyText(difficulty) {
      const difficultyMap = {
        'easy': '简单',
        'medium': '中等',
        'hard': '困难'
      }
      return difficultyMap[difficulty] || '未知难度'
    },

    // 获取正确答案文本
    getCorrectAnswerText() {
      const { question } = this.data
      if (!question || !question.answer) return ''
      
      switch (question.type) {
        case 'single':
          const singleOption = question.options?.find(opt => opt.key === question.answer)
          return singleOption ? `${question.answer}. ${singleOption.value}` : question.answer
        case 'multiple':
          const multipleKeys = question.answer.split(',')
          const multipleOptions = question.options?.filter(opt => multipleKeys.includes(opt.key))
          return multipleOptions?.map(opt => `${opt.key}. ${opt.value}`).join('；') || question.answer
        case 'judge':
          return question.answer === 'true' ? '正确' : '错误'
        case 'fill':
        case 'essay':
          return question.answer
        default:
          return question.answer
      }
    }
  }
})
