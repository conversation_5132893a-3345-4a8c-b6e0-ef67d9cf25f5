// pages/books/books.js
const auth = require('../../utils/auth.js')
const api = require('../../utils/api.js')
const Toast = require('../../utils/toast.js')

Page({
  data: {
    subjectId: '',
    subjectName: '',
    bankId: '',
    bankName: '',
    books: [],
    loading: false
  },

  onLoad(options) {
    const { subjectId, subjectName, bankId, bankName } = options
    
    if (!subjectId || !bankId) {
      Toast.fail('参数错误')
      wx.navigateBack()
      return
    }

    this.setData({
      subjectId,
      subjectName: decodeURIComponent(subjectName || ''),
      bankId,
      bankName: decodeURIComponent(bankName || '')
    })

    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: this.data.bankName || '书籍'
    })

    this.checkLoginAndLoadData()
  },

  // 检查登录状态并加载数据
  checkLoginAndLoadData() {
    if (!auth.requireLogin()) return
    this.loadBooks()
  },

  // 加载书籍列表
  loadBooks() {
    this.setData({ loading: true })
    
    api.books.list({ questionBankId: this.data.bankId })
      .then(res => {
        const books = res.data?.list || []
        this.setData({ books })
      })
      .catch(err => {
        console.error('加载书籍失败:', err)
        Toast.fail('加载书籍失败')
      })
      .finally(() => {
        this.setData({ loading: false })
      })
  },

  // 跳转到章节页面
  goToChapters(e) {
    const book = e.currentTarget.dataset.book
    const params = {
      subjectId: this.data.subjectId,
      subjectName: this.data.subjectName,
      bankId: this.data.bankId,
      bankName: this.data.bankName,
      bookId: book.id,
      bookName: book.name
    }
    
    const query = Object.keys(params).map(key => `${key}=${encodeURIComponent(params[key])}`).join('&')
    
    wx.navigateTo({
      url: `/pages/chapters/chapters?${query}`
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadBooks()
    wx.stopPullDownRefresh()
  }
})
