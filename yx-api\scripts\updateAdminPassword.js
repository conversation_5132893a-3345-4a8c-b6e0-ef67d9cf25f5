const mysql = require('mysql2/promise');
require('dotenv').config();

async function updateAdminPassword() {
  try {
    console.log('🔄 更新管理员密码...');
    
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME
    });
    
    console.log('✅ 数据库连接成功');
    
    // 新的密码哈希 (123456)
    const newPasswordHash = '$2a$10$wM5dgQwmTlo00bHrrdRyQeuYgdxuNemow9Egird2UV2/VoDbe609.';
    
    // 更新admin用户密码
    const [result] = await connection.execute(
      'UPDATE users SET password = ? WHERE username = ?',
      [newPasswordHash, 'admin']
    );
    
    if (result.affectedRows > 0) {
      console.log('✅ 管理员密码更新成功');
      console.log('📝 用户名: admin');
      console.log('📝 密码: 123456');
    } else {
      console.log('❌ 未找到admin用户');
    }
    
    await connection.end();
    
  } catch (error) {
    console.error('❌ 更新密码失败:', error.message);
  }
  
  process.exit(0);
}

updateAdminPassword();
