<template>
  <div class="menu-management">
    <el-card>
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="菜单名称">
            <el-input v-model="searchForm.name" placeholder="请输入菜单名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="启用" value="1"></el-option>
              <el-option label="禁用" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleAdd">新增菜单</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        v-loading="loading"
        row-key="id"
        :tree-props="{children: 'children'}"
        border>
        <el-table-column prop="name" label="菜单名称" width="200" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="icon" label="图标" width="80" align="center">
          <template slot-scope="scope">
            <i :class="scope.row.icon" v-if="scope.row.icon" style="font-size: 16px;"></i>
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column prop="path" label="路径" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.path || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="component" label="组件" show-overflow-tooltip>
          <template slot-scope="scope">
            <span>{{ scope.row.component || '-' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="80" align="center"></el-table-column>
        <el-table-column prop="type" label="类型" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.type === 'menu' ? 'primary' : 'success'" size="small">
              {{ scope.row.type === 'menu' ? '菜单' : '按钮' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80" align="center">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'" size="small">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="300" align="center" fixed="right">
          <template slot-scope="scope">
            <div class="operation-buttons">
              <el-button
                size="mini"
                type="primary"
                icon="el-icon-edit"
                @click="handleEdit(scope.row)"
                plain>
                编辑
              </el-button>
              <el-button
                size="mini"
                type="success"
                icon="el-icon-plus"
                @click="handleAddChild(scope.row)"
                v-if="scope.row.type === 'menu'"
                plain>
                新增子菜单
              </el-button>
              <el-button
                size="mini"
                type="danger"
                icon="el-icon-delete"
                @click="handleDelete(scope.row)"
                plain>
                删除
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="上级菜单" prop="parentId">
          <el-select v-model="form.parentId" placeholder="请选择上级菜单" clearable>
            <el-option label="顶级菜单" :value="0"></el-option>
            <el-option
              v-for="menu in menuOptions"
              :key="menu.id"
              :label="menu.name"
              :value="menu.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="菜单类型" prop="type">
          <el-radio-group v-model="form.type">
            <el-radio label="menu">菜单</el-radio>
            <el-radio label="button">按钮</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="菜单名称" prop="name">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="菜单图标" prop="icon" v-if="form.type === 'menu'">
          <el-input v-model="form.icon" placeholder="如：el-icon-menu">
            <template slot="prepend">
              <i :class="form.icon" v-if="form.icon"></i>
            </template>
          </el-input>
        </el-form-item>
        <el-form-item label="路由路径" prop="path" v-if="form.type === 'menu'">
          <el-input v-model="form.path"></el-input>
        </el-form-item>
        <el-form-item label="组件路径" prop="component" v-if="form.type === 'menu'">
          <el-input v-model="form.component"></el-input>
        </el-form-item>
        <el-form-item label="权限标识" prop="permission" v-if="form.type === 'button'">
          <el-input v-model="form.permission"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from '@/api'
import permissionMixin from '@/mixins/permission'

export default {
  name: 'Menu',
  mixins: [permissionMixin],
  data() {
    return {
      loading: false,
      searchForm: {
        name: '',
        status: ''
      },
      tableData: [],
      dialogVisible: false,
      isEdit: false,
      form: {
        id: null,
        parentId: 0,
        name: '',
        icon: '',
        path: '',
        component: '',
        permission: '',
        type: 'menu',
        sort: 0,
        status: 1
      },
      rules: {
        name: [
          { required: true, message: '请输入菜单名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择菜单类型', trigger: 'change' }
        ]
      },
      menuOptions: []
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑菜单' : '新增菜单'
    }
  },
  methods: {
    async handleSearch() {
      await this.loadData()
    },
    handleReset() {
      this.searchForm = {
        name: '',
        status: ''
      }
      this.loadData()
    },
    handleAdd() {
      this.isEdit = false
      this.form = {
        id: null,
        parentId: 0,
        name: '',
        icon: '',
        path: '',
        component: '',
        permission: '',
        type: 'menu',
        sort: 0,
        status: 1
      }
      this.dialogVisible = true
    },
    handleAddChild(row) {
      this.isEdit = false
      this.form = {
        id: null,
        parentId: row.id,
        name: '',
        icon: '',
        path: '',
        component: '',
        permission: '',
        type: 'menu',
        sort: 0,
        status: 1
      }
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.isEdit = true
      this.form = { ...row }
      this.dialogVisible = true
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除该菜单吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await api.menus.delete(row.id)
        this.$message.success('删除成功')
        await this.loadData()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },
    async handleSubmit() {
      try {
        const valid = await this.$refs.form.validate()
        if (!valid) return

        if (this.isEdit) {
          await api.menus.update(this.form.id, this.form)
          this.$message.success('编辑成功')
        } else {
          await api.menus.create(this.form)
          this.$message.success('新增成功')
        }

        this.dialogVisible = false
        await this.loadData()
      } catch (error) {
        this.$message.error(this.isEdit ? '编辑失败' : '新增失败')
      }
    },
    async loadData() {
      try {
        this.loading = true
        const response = await api.menus.list(this.searchForm)
        // API已经返回树形结构，直接使用
        this.tableData = response.data || []

        // 构建菜单选项
        this.buildMenuOptions(this.tableData)
      } catch (error) {
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    buildMenuOptions(data) {
      this.menuOptions = []
      const traverse = (nodes, currentLevel) => {
        nodes.forEach(node => {
          if (node.type === 'menu') {
            this.menuOptions.push({
              id: node.id,
              name: '　'.repeat(currentLevel) + node.name
            })
            if (node.children && node.children.length > 0) {
              traverse(node.children, currentLevel + 1)
            }
          }
        })
      }
      traverse(data, 0)
    }
  },
  async mounted() {
    await this.loadData()
  }
}
</script>

<style lang="scss" scoped>
.menu-management {
  .search-area {
    margin-bottom: 20px;

    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .operation-buttons {
    display: flex;
    flex-wrap: nowrap;
    gap: 6px;
    justify-content: center;
    align-items: center;

    .el-button {
      margin: 0;
      padding: 5px 10px;
      font-size: 12px;
      white-space: nowrap;

      &.el-button--mini {
        min-width: auto;
        height: 28px;
      }
    }
  }

  // 树形表格样式优化
  ::v-deep .el-table {
    .el-table__row {
      &:hover {
        background-color: #f5f7fa;
      }
    }

    .el-table__expand-icon {
      color: #409eff;
    }
  }
}
</style>
