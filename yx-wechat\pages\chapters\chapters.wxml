<!--pages/chapters/chapters.wxml-->
<view class="container">
  <!-- 章节列表 -->
  <view class="chapters-list" wx:if="{{ chapters.length > 0 }}">
    <view 
      class="chapter-item" 
      wx:for="{{ chapters }}" 
      wx:key="id"
      bind:tap="goToQuestions"
      data-chapter="{{ item }}"
    >
      <view class="chapter-icon">
        <image src="/images/bookmark-o.png" class="chapter-icon-image" />
      </view>
      <view class="chapter-info">
        <view class="chapter-name">{{ item.name }}</view>
        <view class="chapter-progress">
          <view class="progress-bar">
            <view 
              class="progress-inner" 
              style="width: {{ item.progress || 0 }}%; background-color: #1989fa;"
            ></view>
          </view>
          <text class="progress-text">{{ item.progress || 0 }}%</text>
        </view>
        <view class="chapter-stats">
          <text class="stat-text">{{ item.questionCount || 0 }}道题目</text>
          <text class="stat-text" wx:if="{{ item.completedCount }}">
            已完成 {{ item.completedCount }}道
          </text>
        </view>
      </view>
      <image src="/images/arrow.png" class="icon-arrow" />
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:elif="{{ !loading }}">
    <image src="/images/bookmark-o.png" class="empty-icon" />
    <view class="empty-text">该书籍暂无章节</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{ loading }}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
</view>
