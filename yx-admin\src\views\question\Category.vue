<template>
  <div class="category-management">
    <el-card>
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="分类名称">
            <el-input v-model="searchForm.name" placeholder="请输入分类名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="题目类型">
            <el-select v-model="searchForm.questionType" placeholder="请选择题目类型" clearable>
              <el-option label="单选题" value="single"></el-option>
              <el-option label="多选题" value="multiple"></el-option>
              <el-option label="判断题" value="judge"></el-option>
              <el-option label="填空题" value="fill"></el-option>
              <el-option label="简答题" value="essay"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="启用" value="1"></el-option>
              <el-option label="禁用" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleAdd">新增分类</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="分类名称"></el-table-column>
        <el-table-column prop="questionType" label="题目类型" width="120">
          <template slot-scope="scope">
            <el-tag :type="getQuestionTypeColor(scope.row.questionType)">
              {{ getQuestionTypeName(scope.row.questionType) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="difficulty" label="难度等级" width="120">
          <template slot-scope="scope">
            <el-tag :type="getDifficultyColor(scope.row.difficulty)">
              {{ getDifficultyName(scope.row.difficulty) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="questionCount" label="题目数量" width="100">
          <template slot-scope="scope">
            <el-tag type="info">{{ scope.row.questionCount }}题</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述" show-overflow-tooltip></el-table-column>
        <el-table-column prop="sort" label="排序" width="80"></el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160"></el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        style="margin-top: 20px; text-align: right;">
      </el-pagination>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="分类名称" prop="name">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="题目类型" prop="questionType">
          <el-select v-model="form.questionType" placeholder="请选择题目类型" style="width: 100%;">
            <el-option label="单选题" value="single"></el-option>
            <el-option label="多选题" value="multiple"></el-option>
            <el-option label="判断题" value="judge"></el-option>
            <el-option label="填空题" value="fill"></el-option>
            <el-option label="简答题" value="essay"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="难度等级" prop="difficulty">
          <el-select v-model="form.difficulty" placeholder="请选择难度等级" style="width: 100%;">
            <el-option label="简单" value="easy"></el-option>
            <el-option label="中等" value="medium"></el-option>
            <el-option label="困难" value="hard"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" rows="3"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'QuestionCategory',
  data() {
    return {
      loading: false,
      searchForm: {
        name: '',
        questionType: '',
        status: ''
      },
      tableData: [
        {
          id: 1,
          name: '基础计算',
          questionType: 'single',
          difficulty: 'easy',
          questionCount: 120,
          description: '基础的数学计算题目',
          sort: 1,
          status: 1,
          createTime: '2024-01-01 10:00:00'
        },
        {
          id: 2,
          name: '应用题',
          questionType: 'essay',
          difficulty: 'medium',
          questionCount: 85,
          description: '数学应用题，需要分析和计算',
          sort: 2,
          status: 1,
          createTime: '2024-01-01 10:00:00'
        },
        {
          id: 3,
          name: '选择判断',
          questionType: 'judge',
          difficulty: 'easy',
          questionCount: 95,
          description: '判断题，对错选择',
          sort: 3,
          status: 1,
          createTime: '2024-01-01 10:00:00'
        },
        {
          id: 4,
          name: '填空练习',
          questionType: 'fill',
          difficulty: 'medium',
          questionCount: 76,
          description: '填空题，考查基础知识',
          sort: 4,
          status: 1,
          createTime: '2024-01-01 10:00:00'
        }
      ],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 4
      },
      dialogVisible: false,
      isEdit: false,
      form: {
        id: null,
        name: '',
        questionType: '',
        difficulty: '',
        description: '',
        sort: 0,
        status: 1
      },
      rules: {
        name: [
          { required: true, message: '请输入分类名称', trigger: 'blur' }
        ],
        questionType: [
          { required: true, message: '请选择题目类型', trigger: 'change' }
        ],
        difficulty: [
          { required: true, message: '请选择难度等级', trigger: 'change' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑分类' : '新增分类'
    }
  },
  methods: {
    getQuestionTypeName(type) {
      const typeMap = {
        single: '单选题',
        multiple: '多选题',
        judge: '判断题',
        fill: '填空题',
        essay: '简答题'
      }
      return typeMap[type] || type
    },
    getQuestionTypeColor(type) {
      const colorMap = {
        single: 'primary',
        multiple: 'success',
        judge: 'warning',
        fill: 'info',
        essay: 'danger'
      }
      return colorMap[type] || ''
    },
    getDifficultyName(difficulty) {
      const difficultyMap = {
        1: '简单',
        2: '中等',
        3: '困难',
        easy: '简单',
        medium: '中等',
        hard: '困难'
      }
      return difficultyMap[difficulty] || difficulty
    },
    getDifficultyColor(difficulty) {
      const colorMap = {
        1: 'success',
        2: 'warning',
        3: 'danger',
        easy: 'success',
        medium: 'warning',
        hard: 'danger'
      }
      return colorMap[difficulty] || ''
    },
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadData()
    },
    handleReset() {
      this.searchForm = {
        name: '',
        questionType: '',
        status: ''
      }
      this.loadData()
    },
    handleAdd() {
      this.isEdit = false
      this.form = {
        id: null,
        name: '',
        questionType: '',
        difficulty: '',
        description: '',
        sort: 0,
        status: 1
      }
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.isEdit = true
      this.form = { ...row }
      this.dialogVisible = true
    },
    handleDelete(row) {
      this.$confirm('确定要删除该分类吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
        this.loadData()
      })
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$message.success(this.isEdit ? '编辑成功' : '新增成功')
          this.dialogVisible = false
          this.loadData()
        }
      })
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.loadData()
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.loadData()
    },
    loadData() {
      this.loading = true
      // 模拟API调用
      setTimeout(() => {
        this.loading = false
      }, 500)
    }
  },
  mounted() {
    this.loadData()
  }
}
</script>

<style lang="scss" scoped>
.category-management {
  .search-area {
    margin-bottom: 20px;

    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
}
</style>
