/* pages/questions/questions.wxss */

/* 容器样式 */
.container {
  padding: 20rpx;
  min-height: 100vh;
  background-color: #f7f8fa;
}

/* 进度条样式 */
.progress-bar {
  margin-bottom: 24rpx;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8rpx;
  font-size: 24rpx;
  color: #646566;
}

/* 题目内容样式 */
.question-content {
  background-color: #fff;
  border-radius: 12rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(100, 101, 102, 0.08);
}

.question-header {
  display: flex;
  margin-bottom: 16rpx;
}

/* 标签样式 */
.tag {
  display: inline-block;
  padding: 4rpx 12rpx;
  font-size: 20rpx;
  border-radius: 4rpx;
  margin-right: 12rpx;
}

.tag-primary {
  background-color: #e8f3ff;
  color: #1989fa;
}

.tag-success {
  background-color: #e8f9ee;
  color: #07c160;
}

.question-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #323233;
  margin-bottom: 24rpx;
  line-height: 1.5;
}

/* 选项样式 */
.options {
  margin-top: 24rpx;
}

.option-item {
  display: flex;
  align-items: center;
  padding: 24rpx 0;
  border-bottom: 1px solid #ebedf0;
}

.option-text {
  margin-left: 16rpx;
}

/* 填空题和简答题样式 */
.textarea-field {
  width: 100%;
  padding: 20rpx;
  border: 1px solid #ebedf0;
  border-radius: 8rpx;
  font-size: 28rpx;
  min-height: 200rpx;
}

.textarea-container {
  position: relative;
}

.word-limit {
  position: absolute;
  right: 16rpx;
  bottom: 8rpx;
  font-size: 24rpx;
  color: #969799;
}

/* 分割线样式 */
.divider {
  display: flex;
  align-items: center;
  margin: 32rpx 0;
  color: #969799;
  font-size: 28rpx;
  border-top: 1px solid #ebedf0;
  padding-top: 32rpx;
}

/* 答案解析样式 */
.answer-analysis {
  margin-top: 32rpx;
  padding-top: 16rpx;
}

.correct-answer, .analysis-content {
  margin-bottom: 16rpx;
}

.label {
  font-weight: 500;
  color: #323233;
}

.answer {
  color: #07c160;
}

.content {
  color: #646566;
  line-height: 1.5;
}

/* 按钮样式 */
.action-buttons {
  margin-top: 32rpx;
}

.action-button {
  margin-bottom: 16rpx;
  width: 100%;
}

.button-group {
  display: flex;
  justify-content: space-between;
}

.button-group button {
  flex: 1;
  margin: 0 8rpx;
}

.info-button {
  background-color: #1989fa !important;
  color: #fff !important;
}

.success-button {
  background-color: #07c160 !important;
  color: #fff !important;
}

.warning-button {
  background-color: #ff976a !important;
  color: #fff !important;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.icon-placeholder {
  margin-bottom: 16rpx;
}

.placeholder-icon {
  width: 80rpx;
  height: 80rpx;
}

.empty-text {
  color: #969799;
  font-size: 28rpx;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.loading-icon {
  width: 48rpx;
  height: 48rpx;
  border: 4rpx solid #ebedf0;
  border-radius: 50%;
  border-top: 4rpx solid #1989fa;
  animation: spin 1s linear infinite;
  margin-bottom: 16rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  color: #969799;
  font-size: 28rpx;
}
