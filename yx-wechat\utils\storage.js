// utils/storage.js

// 存储数据
function setStorage(key, data) {
  try {
    wx.setStorageSync(key, data)
    return true
  } catch (e) {
    console.error('存储失败:', e)
    return false
  }
}

// 获取数据
function getStorage(key, defaultValue = null) {
  try {
    const data = wx.getStorageSync(key)
    return data || defaultValue
  } catch (e) {
    console.error('获取存储失败:', e)
    return defaultValue
  }
}

// 删除数据
function removeStorage(key) {
  try {
    wx.removeStorageSync(key)
    return true
  } catch (e) {
    console.error('删除存储失败:', e)
    return false
  }
}

// 清空所有数据
function clearStorage() {
  try {
    wx.clearStorageSync()
    return true
  } catch (e) {
    console.error('清空存储失败:', e)
    return false
  }
}

// 学习进度相关
const progress = {
  // 保存学习进度
  saveProgress(subjectId, questionBankId, bookId, chapterId, progress) {
    const key = `progress_${subjectId}_${questionBankId}_${bookId}_${chapterId}`
    return setStorage(key, progress)
  },
  
  // 获取学习进度
  getProgress(subjectId, questionBankId, bookId, chapterId) {
    const key = `progress_${subjectId}_${questionBankId}_${bookId}_${chapterId}`
    return getStorage(key, { completed: 0, total: 0, correctRate: 0 })
  },
  
  // 保存答题记录
  saveAnswerRecord(questionId, answer, isCorrect) {
    const records = getStorage('answer_records', [])
    const record = {
      questionId,
      answer,
      isCorrect,
      timestamp: Date.now()
    }
    records.push(record)
    return setStorage('answer_records', records)
  },
  
  // 获取答题记录
  getAnswerRecords() {
    return getStorage('answer_records', [])
  },
  
  // 获取统计数据
  getStatistics() {
    const records = this.getAnswerRecords()
    const total = records.length
    const correct = records.filter(r => r.isCorrect).length
    const correctRate = total > 0 ? (correct / total * 100).toFixed(1) : 0
    
    return {
      total,
      correct,
      wrong: total - correct,
      correctRate: parseFloat(correctRate)
    }
  }
}

module.exports = {
  setStorage,
  getStorage,
  removeStorage,
  clearStorage,
  progress
}
