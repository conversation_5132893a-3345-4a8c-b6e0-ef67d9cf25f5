const Router = require('koa-router');
const { response } = require('../utils/response');

// 导入各模块路由
const authRoutes = require('./auth');
const userRoutes = require('./users');
const roleRoutes = require('./roles');
const menuRoutes = require('./menus');
const permissionRoutes = require('./permissions');
const subjectRoutes = require('./subjects');
const questionBankRoutes = require('./questionBanks');
const bookRoutes = require('./books');
const chapterRoutes = require('./chapters');
const questionCategoryRoutes = require('./questionCategories');
const questionRoutes = require('./questions');
const logRoutes = require('./logs');

const router = new Router();

// 健康检查
router.get('/health', async (ctx) => {
  ctx.body = response.success({
    status: 'ok',
    timestamp: new Date().toISOString(),
    version: '1.0.0'
  }, 'API服务运行正常');
});

// API根路径
router.get('/', async (ctx) => {
  ctx.body = response.success({
    name: 'YX刷题小程序API',
    version: '1.0.0',
    description: 'YX刷题小程序后端API接口',
    author: 'YX Team'
  }, '欢迎使用YX刷题小程序API');
});

// 注册路由
router.use('/api/auth', authRoutes.routes(), authRoutes.allowedMethods());
router.use('/api/users', userRoutes.routes(), userRoutes.allowedMethods());
router.use('/api/roles', roleRoutes.routes(), roleRoutes.allowedMethods());
router.use('/api/menus', menuRoutes.routes(), menuRoutes.allowedMethods());
router.use('/api/permissions', permissionRoutes.routes(), permissionRoutes.allowedMethods());
router.use('/api/subjects', subjectRoutes.routes(), subjectRoutes.allowedMethods());
router.use('/api/question-banks', questionBankRoutes.routes(), questionBankRoutes.allowedMethods());
router.use('/api/books', bookRoutes.routes(), bookRoutes.allowedMethods());
router.use('/api/chapters', chapterRoutes.routes(), chapterRoutes.allowedMethods());
router.use('/api/question-categories', questionCategoryRoutes.routes(), questionCategoryRoutes.allowedMethods());
router.use('/api/questions', questionRoutes.routes(), questionRoutes.allowedMethods());
router.use('/api/logs', logRoutes.routes(), logRoutes.allowedMethods());

module.exports = router;
