/* pages/chapters/chapters.wxss */
.container {
  padding: 24rpx;
}

/* 章节列表 */
.chapters-list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.chapter-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #ebedf0;
  transition: background-color 0.2s;
}

.chapter-item:last-child {
  border-bottom: none;
}

.chapter-item:active {
  background-color: #f7f8fa;
}

.chapter-icon {
  width: 60rpx;
  height: 60rpx;
  background: #f7f8fa;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.chapter-info {
  flex: 1;
  margin-right: 16rpx;
}

.chapter-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 16rpx;
}

.chapter-progress {
  display: flex;
  align-items: center;
  margin-bottom: 12rpx;
}

.progress-text {
  font-size: 24rpx;
  color: #969799;
  margin-left: 16rpx;
  min-width: 60rpx;
}

.chapter-stats {
  display: flex;
  gap: 24rpx;
}

.stat-text {
  font-size: 24rpx;
  color: #969799;
}

/* 进度条样式 */
.progress-bar {
  width: 100%;
  height: 6rpx;
  background-color: #ebedf0;
  border-radius: 100rpx;
  overflow: hidden;
}

.progress-inner {
  height: 100%;
  border-radius: 100rpx;
  transition: width 0.3s ease;
}

/* 图标样式 */
.chapter-icon-image {
  width: 32rpx;
  height: 32rpx;
}

.icon-arrow {
  width: 32rpx;
  height: 32rpx;
}

.empty-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
}

/* 加载样式 */
.loading-spinner {
  width: 24rpx;
  height: 24rpx;
  border: 3rpx solid #c8c9cc;
  border-top-color: transparent;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #969799;
  margin-top: 24rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #969799;
  margin-top: 16rpx;
}
