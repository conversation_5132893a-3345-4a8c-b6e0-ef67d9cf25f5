const { query } = require('../config/database');
const { hashPassword } = require('../utils/crypto');
require('dotenv').config();

async function resetAdminPassword() {
  try {
    console.log('🔄 重置管理员密码...');
    
    // 生成新的密码哈希
    const newPassword = '123456';
    const hashedPassword = await hashPassword(newPassword);
    
    console.log('新密码哈希:', hashedPassword);
    
    // 更新admin用户密码
    const result = await query(
      'UPDATE users SET password = ? WHERE username = ?',
      [hashedPassword, 'admin']
    );
    
    if (result.affectedRows > 0) {
      console.log('✅ 管理员密码重置成功');
      console.log('📝 用户名: admin');
      console.log('📝 密码: 123456');
    } else {
      console.log('❌ 未找到admin用户');
    }
    
  } catch (error) {
    console.error('❌ 重置密码失败:', error.message);
  }
  
  process.exit(0);
}

resetAdminPassword();
