const Router = require('koa-router');
const { response } = require('../utils/response');
const { validation } = require('../utils/validation');
const { query } = require('../config/database');
const { authMiddleware, permissionMiddleware } = require('../middleware/auth');

const router = new Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取题库列表
router.get('/', permissionMiddleware('question:bank'), async (ctx) => {
  const { page = 1, pageSize = 10, name, subjectId, status } = ctx.query;

  let whereClause = 'WHERE 1=1';
  const params = [];

  if (name) {
    whereClause += ' AND qb.name LIKE ?';
    params.push(`%${name}%`);
  }

  if (subjectId) {
    whereClause += ' AND qb.subject_id = ?';
    params.push(subjectId);
  }

  if (status !== undefined) {
    whereClause += ' AND qb.status = ?';
    params.push(status);
  }

  // 查询总数
  const countSql = `SELECT COUNT(*) as total FROM question_banks qb ${whereClause}`;
  const countResult = await query(countSql, params);
  const total = countResult[0].total;

  // 查询数据
  const offset = (page - 1) * pageSize;
  const dataSql = `
    SELECT qb.id, qb.name, qb.subject_id, qb.description, qb.status, qb.created_time, qb.updated_time,
           s.name as subject_name, s.code as subject_code
    FROM question_banks qb
    LEFT JOIN subjects s ON qb.subject_id = s.id
    ${whereClause}
    ORDER BY qb.created_time DESC
    LIMIT ${parseInt(pageSize)} OFFSET ${parseInt(offset)}
  `;

  const questionBanks = await query(dataSql, params);

  ctx.body = response.page(questionBanks, total, page, pageSize);
});

// 获取题库详情
router.get('/:id', permissionMiddleware('question:bank'), async (ctx) => {
  const { id } = ctx.params;

  const questionBanks = await query(`
    SELECT qb.id, qb.name, qb.subject_id, qb.description, qb.status, qb.created_time, qb.updated_time,
           s.name as subject_name, s.code as subject_code
    FROM question_banks qb
    LEFT JOIN subjects s ON qb.subject_id = s.id
    WHERE qb.id = ?
  `, [id]);

  if (questionBanks.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('题库不存在', 404);
    return;
  }

  ctx.body = response.success(questionBanks[0]);
});

// 创建题库
router.post('/', permissionMiddleware('question:bank'), async (ctx) => {
  const { name, subjectId, description } = ctx.request.body;

  // 参数验证
  validation.required(name, '题库名称');
  validation.required(subjectId, '学科ID');
  validation.length(name, 1, 100, '题库名称');

  // 检查学科是否存在
  const subjects = await query(
    'SELECT id FROM subjects WHERE id = ? AND status = 1',
    [subjectId]
  );

  if (subjects.length === 0) {
    ctx.status = 400;
    ctx.body = response.error('学科不存在或已禁用', 400);
    return;
  }

  // 创建题库
  const result = await query(
    'INSERT INTO question_banks (name, subject_id, description, status, created_time) VALUES (?, ?, ?, ?, ?)',
    [name, subjectId, description || null, 1, new Date()]
  );

  ctx.body = response.success({ id: result.insertId, name, subjectId }, '题库创建成功');
});

// 更新题库
router.put('/:id', permissionMiddleware('question:bank'), async (ctx) => {
  const { id } = ctx.params;
  const { name, subjectId, description, status } = ctx.request.body;

  // 参数验证
  validation.required(name, '题库名称');
  validation.required(subjectId, '学科ID');
  validation.length(name, 1, 100, '题库名称');

  // 检查题库是否存在
  const existingQuestionBanks = await query(
    'SELECT id FROM question_banks WHERE id = ?',
    [id]
  );

  if (existingQuestionBanks.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('题库不存在', 404);
    return;
  }

  // 检查学科是否存在
  const subjects = await query(
    'SELECT id FROM subjects WHERE id = ? AND status = 1',
    [subjectId]
  );

  if (subjects.length === 0) {
    ctx.status = 400;
    ctx.body = response.error('学科不存在或已禁用', 400);
    return;
  }

  // 更新题库
  await query(
    'UPDATE question_banks SET name = ?, subject_id = ?, description = ?, status = ?, updated_time = ? WHERE id = ?',
    [name, subjectId, description || null, status, new Date(), id]
  );

  ctx.body = response.success(null, '题库更新成功');
});

// 删除题库
router.delete('/:id', permissionMiddleware('question:bank'), async (ctx) => {
  const { id } = ctx.params;

  // 检查题库是否存在
  const existingQuestionBanks = await query(
    'SELECT id FROM question_banks WHERE id = ?',
    [id]
  );

  if (existingQuestionBanks.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('题库不存在', 404);
    return;
  }

  // 检查是否有关联的书籍
  const books = await query(
    'SELECT id FROM books WHERE question_bank_id = ?',
    [id]
  );

  if (books.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('该题库下存在书籍，无法删除', 400);
    return;
  }

  // 删除题库
  await query('DELETE FROM question_banks WHERE id = ?', [id]);

  ctx.body = response.success(null, '题库删除成功');
});

module.exports = router;
