// utils/api.js
const app = getApp()

// 请求封装
function request(url, options = {}) {
  return new Promise((resolve, reject) => {
    const { method = 'GET', data = {}, header = {} } = options
    
    // 添加认证头
    if (app.globalData.token) {
      header.Authorization = `Bearer ${app.globalData.token}`
    }
    
    wx.request({
      url: `${app.globalData.baseUrl}${url}`,
      method,
      data,
      header: {
        'Content-Type': 'application/json',
        ...header
      },
      success: (res) => {
        if (res.statusCode === 200) {
          if (res.data.code === 200) {
            resolve(res.data)
          } else {
            wx.showToast({
              title: res.data.message || '请求失败',
              icon: 'none'
            })
            reject(res.data)
          }
        } else if (res.statusCode === 401) {
          // 未授权，清除登录信息并跳转到登录页
          app.clearUserInfo()
          wx.reLaunch({
            url: '/pages/login/login'
          })
          reject(res.data)
        } else {
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          })
          reject(res)
        }
      },
      fail: (err) => {
        wx.showToast({
          title: '网络连接失败',
          icon: 'none'
        })
        reject(err)
      }
    })
  })
}

// API 接口
const api = {
  // 认证相关
  auth: {
    login: (data) => request('/auth/login', { method: 'POST', data }),
    register: (data) => request('/auth/register', { method: 'POST', data }),
    profile: () => request('/auth/profile')
  },
  
  // 学科管理
  subjects: {
    list: (params) => request('/subjects', { method: 'GET', data: params }),
    all: () => request('/subjects/all'),
    detail: (id) => request(`/subjects/${id}`)
  },
  
  // 题库管理
  questionBanks: {
    list: (params) => request('/question-banks', { method: 'GET', data: params }),
    detail: (id) => request(`/question-banks/${id}`)
  },
  
  // 书籍管理
  books: {
    list: (params) => request('/books', { method: 'GET', data: params }),
    detail: (id) => request(`/books/${id}`)
  },
  
  // 章节管理
  chapters: {
    list: (params) => request('/chapters', { method: 'GET', data: params }),
    detail: (id) => request(`/chapters/${id}`)
  },
  
  // 题目管理
  questions: {
    list: (params) => request('/questions', { method: 'GET', data: params }),
    detail: (id) => request(`/questions/${id}`)
  }
}

module.exports = api
