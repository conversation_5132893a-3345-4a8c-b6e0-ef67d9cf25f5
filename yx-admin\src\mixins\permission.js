import { mapGetters } from 'vuex'

export default {
  computed: {
    ...mapGetters('user', ['userPermissions'])
  },
  methods: {
    /**
     * 检查是否有权限
     * @param {String|Array} permission 权限码或权限码数组
     * @returns {Boolean}
     */
    hasPermission(permission) {
      if (!permission) return true
      
      const permissions = this.userPermissions || []
      
      if (Array.isArray(permission)) {
        // 数组形式，只要有一个权限就返回true
        return permission.some(p => permissions.includes(p))
      } else {
        // 字符串形式
        return permissions.includes(permission)
      }
    },
    
    /**
     * 检查是否有所有权限
     * @param {Array} permissions 权限码数组
     * @returns {Boolean}
     */
    hasAllPermissions(permissions) {
      if (!permissions || !Array.isArray(permissions)) return true
      
      const userPermissions = this.userPermissions || []
      return permissions.every(p => userPermissions.includes(p))
    },
    
    /**
     * 检查是否有任一权限
     * @param {Array} permissions 权限码数组
     * @returns {Boolean}
     */
    hasAnyPermission(permissions) {
      if (!permissions || !Array.isArray(permissions)) return true
      
      const userPermissions = this.userPermissions || []
      return permissions.some(p => userPermissions.includes(p))
    }
  }
}
