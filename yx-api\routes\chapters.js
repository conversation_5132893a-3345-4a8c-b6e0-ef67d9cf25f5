const Router = require('koa-router');
const { response } = require('../utils/response');
const { validation } = require('../utils/validation');
const { query } = require('../config/database');
const { authMiddleware, permissionMiddleware } = require('../middleware/auth');

const router = new Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取章节列表
router.get('/', permissionMiddleware('question:chapter'), async (ctx) => {
  const { page = 1, pageSize = 10, name, bookId, status } = ctx.query;

  let whereClause = 'WHERE 1=1';
  const params = [];

  if (name && name !== '') {
    whereClause += ' AND c.name LIKE ?';
    params.push(`%${name}%`);
  }

  if (bookId && bookId !== '') {
    whereClause += ' AND c.book_id = ?';
    params.push(bookId);
  }

  if (status !== undefined && status !== '') {
    whereClause += ' AND c.status = ?';
    params.push(status);
  }

  // 查询总数
  const countSql = `SELECT COUNT(*) as total FROM chapters c ${whereClause}`;
  const countResult = await query(countSql, params);
  const total = countResult[0].total;

  // 查询数据
  const offset = (page - 1) * pageSize;
  const dataSql = `
    SELECT c.id, c.name, c.book_id, c.description, c.sort, c.status, c.created_time, c.updated_time,
           b.name as book_name
    FROM chapters c
    LEFT JOIN books b ON c.book_id = b.id
    ${whereClause}
    ORDER BY c.sort ASC, c.created_time DESC
    LIMIT ${parseInt(pageSize)} OFFSET ${parseInt(offset)}
  `;

  const chapters = await query(dataSql, params);

  ctx.body = response.page(chapters, total, page, pageSize);
});

// 创建章节
router.post('/', permissionMiddleware('question:chapter'), async (ctx) => {
  const { name, bookId, description, sort } = ctx.request.body;

  // 参数验证
  validation.required(name, '章节名称');
  validation.required(bookId, '书籍ID');
  validation.length(name, 1, 100, '章节名称');

  // 检查书籍是否存在
  const books = await query('SELECT id FROM books WHERE id = ? AND status = 1', [bookId]);

  if (books.length === 0) {
    ctx.status = 400;
    ctx.body = response.error('书籍不存在或已禁用', 400);
    return;
  }

  // 创建章节
  const result = await query(
    'INSERT INTO chapters (name, book_id, description, sort, status, created_time) VALUES (?, ?, ?, ?, ?, ?)',
    [name, bookId, description || null, sort || 0, 1, new Date()]
  );

  ctx.body = response.success({ id: result.insertId, name, bookId }, '章节创建成功');
});

// 更新章节
router.put('/:id', permissionMiddleware('question:chapter'), async (ctx) => {
  const { id } = ctx.params;
  const { name, bookId, description, sort, status } = ctx.request.body;

  // 参数验证
  validation.required(name, '章节名称');
  validation.required(bookId, '书籍ID');
  validation.length(name, 1, 100, '章节名称');

  // 检查章节是否存在
  const existingChapters = await query('SELECT id FROM chapters WHERE id = ?', [id]);

  if (existingChapters.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('章节不存在', 404);
    return;
  }

  // 检查书籍是否存在
  const books = await query('SELECT id FROM books WHERE id = ? AND status = 1', [bookId]);

  if (books.length === 0) {
    ctx.status = 400;
    ctx.body = response.error('书籍不存在或已禁用', 400);
    return;
  }

  // 更新章节
  await query(
    'UPDATE chapters SET name = ?, book_id = ?, description = ?, sort = ?, status = ?, updated_time = ? WHERE id = ?',
    [name, bookId, description || null, sort || 0, status, new Date(), id]
  );

  ctx.body = response.success(null, '章节更新成功');
});

// 删除章节
router.delete('/:id', permissionMiddleware('question:chapter'), async (ctx) => {
  const { id } = ctx.params;

  // 检查章节是否存在
  const existingChapters = await query('SELECT id FROM chapters WHERE id = ?', [id]);

  if (existingChapters.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('章节不存在', 404);
    return;
  }

  // 检查是否有关联的题目
  const questions = await query('SELECT id FROM questions WHERE chapter_id = ?', [id]);

  if (questions.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('该章节下存在题目，无法删除', 400);
    return;
  }

  // 删除章节
  await query('DELETE FROM chapters WHERE id = ?', [id]);

  ctx.body = response.success(null, '章节删除成功');
});

module.exports = router;
