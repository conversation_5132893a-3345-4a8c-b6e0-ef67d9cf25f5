const Router = require('koa-router');
const { response } = require('../utils/response');
const { hashPassword, comparePassword, generateToken } = require('../utils/crypto');
const { validation } = require('../utils/validation');
const { query } = require('../config/database');
const { authMiddleware } = require('../middleware/auth');
const moment = require('moment');

const router = new Router();

// 用户登录
router.post('/login', async (ctx) => {
  const { username, password } = ctx.request.body;

  // 参数验证
  validation.required(username, '用户名');
  validation.required(password, '密码');

  // 查询用户
  const users = await query(
    'SELECT id, username, email, password, status FROM users WHERE username = ? OR email = ?',
    [username, username]
  );

  if (users.length === 0) {
    ctx.status = 401;
    ctx.body = response.error('用户名或密码错误', 401);
    return;
  }

  const user = users[0];

  // 检查用户状态
  if (user.status !== 1) {
    ctx.status = 401;
    ctx.body = response.error('用户已被禁用', 401);
    return;
  }

  // 验证密码
  const isPasswordValid = await comparePassword(password, user.password);
  if (!isPasswordValid) {
    ctx.status = 401;
    ctx.body = response.error('用户名或密码错误', 401);
    return;
  }

  // 生成token
  const token = generateToken({
    userId: user.id,
    username: user.username
  });

  // 记录登录日志
  await query(
    'INSERT INTO login_logs (user_id, username, ip_address, user_agent, login_time) VALUES (?, ?, ?, ?, ?)',
    [user.id, user.username, ctx.ip, ctx.headers['user-agent'] || '', new Date()]
  );

  // 更新最后登录时间
  await query(
    'UPDATE users SET last_login_time = ? WHERE id = ?',
    [new Date(), user.id]
  );

  ctx.body = response.success({
    token,
    user: {
      id: user.id,
      username: user.username,
      email: user.email
    }
  }, '登录成功');
});

// 用户注册
router.post('/register', async (ctx) => {
  const { username, email, password, confirmPassword } = ctx.request.body;

  // 参数验证
  validation.required(username, '用户名');
  validation.required(email, '邮箱');
  validation.required(password, '密码');
  validation.required(confirmPassword, '确认密码');

  if (!validation.isUsername(username)) {
    ctx.status = 400;
    ctx.body = response.error('用户名格式不正确（3-20位字母数字下划线）', 400);
    return;
  }

  if (!validation.isEmail(email)) {
    ctx.status = 400;
    ctx.body = response.error('邮箱格式不正确', 400);
    return;
  }

  if (!validation.isStrongPassword(password)) {
    ctx.status = 400;
    ctx.body = response.error('密码强度不够（至少6位，包含字母和数字）', 400);
    return;
  }

  if (password !== confirmPassword) {
    ctx.status = 400;
    ctx.body = response.error('两次密码输入不一致', 400);
    return;
  }

  // 检查用户名是否已存在
  const existingUsers = await query(
    'SELECT id FROM users WHERE username = ? OR email = ?',
    [username, email]
  );

  if (existingUsers.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('用户名或邮箱已存在', 400);
    return;
  }

  // 加密密码
  const hashedPassword = await hashPassword(password);

  // 创建用户
  const result = await query(
    'INSERT INTO users (username, email, password, status, created_time) VALUES (?, ?, ?, ?, ?)',
    [username, email, hashedPassword, 1, new Date()]
  );

  ctx.body = response.success({
    id: result.insertId,
    username,
    email
  }, '注册成功');
});

// 获取当前用户信息
router.get('/profile', authMiddleware, async (ctx) => {
  const userId = ctx.state.user.id;

  // 查询用户详细信息
  const users = await query(`
    SELECT u.id, u.username, u.email, u.avatar, u.phone, u.status,
           u.created_time, u.last_login_time
    FROM users u
    WHERE u.id = ?
  `, [userId]);

  if (users.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('用户不存在', 404);
    return;
  }

  const user = users[0];

  // 查询用户角色
  const roles = await query(`
    SELECT r.id, r.name, r.code
    FROM roles r
    JOIN user_roles ur ON r.id = ur.role_id
    WHERE ur.user_id = ? AND r.status = 1
  `, [userId]);

  // 查询用户权限
  const permissions = await query(`
    SELECT DISTINCT p.id, p.name, p.code, p.type
    FROM permissions p
    JOIN role_permissions rp ON p.id = rp.permission_id
    JOIN user_roles ur ON rp.role_id = ur.role_id
    WHERE ur.user_id = ? AND p.status = 1
  `, [userId]);

  ctx.body = response.success({
    ...user,
    roles,
    permissions
  }, '获取用户信息成功');
});

module.exports = router;
