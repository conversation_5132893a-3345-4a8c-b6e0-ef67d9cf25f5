// 统一响应格式
const response = {
  // 成功响应
  success: (data = null, message = '操作成功') => {
    return {
      code: 200,
      message,
      data,
      timestamp: new Date().toISOString()
    };
  },
  
  // 错误响应
  error: (message = '操作失败', code = 500, data = null) => {
    return {
      code,
      message,
      data,
      timestamp: new Date().toISOString()
    };
  },
  
  // 分页响应
  page: (data, total, page = 1, pageSize = 10, message = '查询成功') => {
    return {
      code: 200,
      message,
      data: {
        list: data,
        pagination: {
          total,
          page: parseInt(page),
          pageSize: parseInt(pageSize),
          totalPages: Math.ceil(total / pageSize)
        }
      },
      timestamp: new Date().toISOString()
    };
  }
};

module.exports = {
  response
};
