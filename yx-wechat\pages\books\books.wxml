<!--pages/books/books.wxml-->
<view class="container">
  <!-- 书籍列表 -->
  <view class="books-list" wx:if="{{ books.length > 0 }}">
    <view 
      class="book-item" 
      wx:for="{{ books }}" 
      wx:key="id"
      bind:tap="goToChapters"
      data-book="{{ item }}"
    >
      <view class="book-cover">
        <image src="/images/notes-o.png" class="book-cover-image" />
      </view>
      <view class="book-info">
        <view class="book-name">{{ item.name }}</view>
        <view class="book-meta">
          <view class="tag tag-primary">{{ item.grade || '通用' }}</view>
          <view class="tag tag-success">{{ item.version || '标准版' }}</view>
        </view>
        <view class="book-stats">
          <text class="stat-text">{{ item.chapterCount || 0 }}个章节</text>
          <text class="stat-text">{{ item.questionCount || 0 }}道题目</text>
        </view>
      </view>
      <image src="/images/arrow.png" class="icon-arrow" />
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:elif="{{ !loading }}">
    <image src="/images/notes-o.png" class="empty-icon" />
    <view class="empty-text">该题库暂无书籍</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{ loading }}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
</view>
