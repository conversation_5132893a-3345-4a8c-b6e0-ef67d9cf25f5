import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import router from '@/router'

// 创建axios实例
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API || 'http://localhost:3000/api',
  timeout: 10000
})

// 请求拦截器
service.interceptors.request.use(
  config => {
    // 添加token到请求头
    const token = store.getters['user/token']
    if (token) {
      config.headers['Authorization'] = `Bearer ${token}`
    }
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
service.interceptors.response.use(
  response => {
    const res = response.data
    
    // 如果返回的状态码不是200，则显示错误信息
    if (res.code !== 200) {
      Message({
        message: res.message || '请求失败',
        type: 'error',
        duration: 5 * 1000
      })
      
      // 401: 未授权，跳转到登录页
      if (res.code === 401) {
        store.dispatch('user/logout')
        router.push('/login')
      }
      
      return Promise.reject(new Error(res.message || '请求失败'))
    } else {
      return res
    }
  },
  error => {
    console.error('响应错误:', error)
    
    let message = '网络错误'
    if (error.response) {
      switch (error.response.status) {
        case 401:
          message = '未授权，请重新登录'
          store.dispatch('user/logout')
          router.push('/login')
          break
        case 403:
          message = '权限不足'
          break
        case 404:
          message = '请求的资源不存在'
          break
        case 500:
          message = '服务器内部错误'
          break
        default:
          message = error.response.data?.message || '请求失败'
      }
    }
    
    Message({
      message,
      type: 'error',
      duration: 5 * 1000
    })
    
    return Promise.reject(error)
  }
)

// API接口定义
export const api = {
  // 认证相关
  auth: {
    login: (data) => service.post('/auth/login', data),
    logout: () => service.post('/auth/logout'),
    profile: () => service.get('/auth/profile')
  },
  
  // 用户管理
  users: {
    list: (params) => service.get('/users', { params }),
    create: (data) => service.post('/users', data),
    update: (id, data) => service.put(`/users/${id}`, data),
    delete: (id) => service.delete(`/users/${id}`),
    resetPassword: (id, data) => service.put(`/users/${id}/reset-password`, data)
  },
  
  // 角色管理
  roles: {
    list: (params) => service.get('/roles', { params }),
    all: () => service.get('/roles/all'),
    create: (data) => service.post('/roles', data),
    update: (id, data) => service.put(`/roles/${id}`, data),
    delete: (id) => service.delete(`/roles/${id}`),
    permissions: (id) => service.get(`/roles/${id}/permissions`),
    assignPermissions: (id, data) => service.put(`/roles/${id}/permissions`, data)
  },
  
  // 菜单管理
  menus: {
    list: () => service.get('/menus'),
    all: () => service.get('/menus/all'),
    user: () => service.get('/menus/user'),
    create: (data) => service.post('/menus', data),
    update: (id, data) => service.put(`/menus/${id}`, data),
    delete: (id) => service.delete(`/menus/${id}`)
  },
  
  // 权限管理
  permissions: {
    list: () => service.get('/permissions'),
    all: () => service.get('/permissions/all'),
    create: (data) => service.post('/permissions', data),
    update: (id, data) => service.put(`/permissions/${id}`, data),
    delete: (id) => service.delete(`/permissions/${id}`)
  },
  
  // 学科管理
  subjects: {
    list: (params) => service.get('/subjects', { params }),
    all: () => service.get('/subjects/all'),
    create: (data) => service.post('/subjects', data),
    update: (id, data) => service.put(`/subjects/${id}`, data),
    delete: (id) => service.delete(`/subjects/${id}`)
  },
  
  // 题库管理
  questionBanks: {
    list: (params) => service.get('/question-banks', { params }),
    create: (data) => service.post('/question-banks', data),
    update: (id, data) => service.put(`/question-banks/${id}`, data),
    delete: (id) => service.delete(`/question-banks/${id}`)
  },
  
  // 书籍管理
  books: {
    list: (params) => service.get('/books', { params }),
    create: (data) => service.post('/books', data),
    update: (id, data) => service.put(`/books/${id}`, data),
    delete: (id) => service.delete(`/books/${id}`)
  },
  
  // 章节管理
  chapters: {
    list: (params) => service.get('/chapters', { params }),
    create: (data) => service.post('/chapters', data),
    update: (id, data) => service.put(`/chapters/${id}`, data),
    delete: (id) => service.delete(`/chapters/${id}`)
  },
  
  // 题目分类管理
  questionCategories: {
    list: (params) => service.get('/question-categories', { params }),
    create: (data) => service.post('/question-categories', data),
    update: (id, data) => service.put(`/question-categories/${id}`, data),
    delete: (id) => service.delete(`/question-categories/${id}`)
  },
  
  // 题目管理
  questions: {
    list: (params) => service.get('/questions', { params }),
    create: (data) => service.post('/questions', data),
    update: (id, data) => service.put(`/questions/${id}`, data),
    delete: (id) => service.delete(`/questions/${id}`)
  },
  
  // 系统日志
  logs: {
    list: (params) => service.get('/logs', { params })
  }
}

export default service
