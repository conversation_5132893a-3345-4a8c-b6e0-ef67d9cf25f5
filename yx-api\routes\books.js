const Router = require('koa-router');
const { response } = require('../utils/response');
const { validation } = require('../utils/validation');
const { query } = require('../config/database');
const { authMiddleware, permissionMiddleware } = require('../middleware/auth');

const router = new Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取书籍列表
router.get('/', permissionMiddleware('question:book'), async (ctx) => {
  const { page = 1, pageSize = 10, name, questionBankId, status } = ctx.query;

  let whereClause = 'WHERE 1=1';
  const params = [];

  if (name && name !== '') {
    whereClause += ' AND b.name LIKE ?';
    params.push(`%${name}%`);
  }

  if (questionBankId && questionBankId !== '') {
    whereClause += ' AND b.question_bank_id = ?';
    params.push(questionBankId);
  }

  if (status !== undefined && status !== '') {
    whereClause += ' AND b.status = ?';
    params.push(status);
  }

  // 查询总数
  const countSql = `SELECT COUNT(*) as total FROM books b ${whereClause}`;
  const countResult = await query(countSql, params);
  const total = countResult[0].total;

  // 查询数据
  const offset = (page - 1) * pageSize;
  const dataSql = `
    SELECT b.id, b.name, b.question_bank_id, b.description, b.status, b.created_time, b.updated_time,
           qb.name as question_bank_name
    FROM books b
    LEFT JOIN question_banks qb ON b.question_bank_id = qb.id
    ${whereClause}
    ORDER BY b.created_time DESC
    LIMIT ${parseInt(pageSize)} OFFSET ${parseInt(offset)}
  `;

  const books = await query(dataSql, params);

  ctx.body = response.page(books, total, page, pageSize);
});

// 获取书籍详情
router.get('/:id', permissionMiddleware('question:book'), async (ctx) => {
  const { id } = ctx.params;

  const books = await query(`
    SELECT b.id, b.name, b.question_bank_id, b.description, b.status, b.created_time, b.updated_time,
           qb.name as question_bank_name
    FROM books b
    LEFT JOIN question_banks qb ON b.question_bank_id = qb.id
    WHERE b.id = ?
  `, [id]);

  if (books.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('书籍不存在', 404);
    return;
  }

  ctx.body = response.success(books[0]);
});

// 创建书籍
router.post('/', permissionMiddleware('question:book'), async (ctx) => {
  const { name, questionBankId, description } = ctx.request.body;

  // 参数验证
  validation.required(name, '书籍名称');
  validation.required(questionBankId, '题库ID');
  validation.length(name, 1, 100, '书籍名称');

  // 检查题库是否存在
  const questionBanks = await query(
    'SELECT id FROM question_banks WHERE id = ? AND status = 1',
    [questionBankId]
  );

  if (questionBanks.length === 0) {
    ctx.status = 400;
    ctx.body = response.error('题库不存在或已禁用', 400);
    return;
  }

  // 创建书籍
  const result = await query(
    'INSERT INTO books (name, question_bank_id, description, status, created_time) VALUES (?, ?, ?, ?, ?)',
    [name, questionBankId, description || null, 1, new Date()]
  );

  ctx.body = response.success({ id: result.insertId, name, questionBankId }, '书籍创建成功');
});

// 更新书籍
router.put('/:id', permissionMiddleware('question:book'), async (ctx) => {
  const { id } = ctx.params;
  const { name, questionBankId, description, status } = ctx.request.body;

  // 参数验证
  validation.required(name, '书籍名称');
  validation.required(questionBankId, '题库ID');
  validation.length(name, 1, 100, '书籍名称');

  // 检查书籍是否存在
  const existingBooks = await query('SELECT id FROM books WHERE id = ?', [id]);

  if (existingBooks.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('书籍不存在', 404);
    return;
  }

  // 检查题库是否存在
  const questionBanks = await query(
    'SELECT id FROM question_banks WHERE id = ? AND status = 1',
    [questionBankId]
  );

  if (questionBanks.length === 0) {
    ctx.status = 400;
    ctx.body = response.error('题库不存在或已禁用', 400);
    return;
  }

  // 更新书籍
  await query(
    'UPDATE books SET name = ?, question_bank_id = ?, description = ?, status = ?, updated_time = ? WHERE id = ?',
    [name, questionBankId, description || null, status, new Date(), id]
  );

  ctx.body = response.success(null, '书籍更新成功');
});

// 删除书籍
router.delete('/:id', permissionMiddleware('question:book'), async (ctx) => {
  const { id } = ctx.params;

  // 检查书籍是否存在
  const existingBooks = await query('SELECT id FROM books WHERE id = ?', [id]);

  if (existingBooks.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('书籍不存在', 404);
    return;
  }

  // 检查是否有关联的章节
  const chapters = await query('SELECT id FROM chapters WHERE book_id = ?', [id]);

  if (chapters.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('该书籍下存在章节，无法删除', 400);
    return;
  }

  // 删除书籍
  await query('DELETE FROM books WHERE id = ?', [id]);

  ctx.body = response.success(null, '书籍删除成功');
});

module.exports = router;
