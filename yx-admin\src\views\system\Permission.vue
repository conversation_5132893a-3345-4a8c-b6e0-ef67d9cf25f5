<template>
  <div class="permission-management">
    <el-card>
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="权限名称">
            <el-input v-model="searchForm.name" placeholder="请输入权限名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="权限类型">
            <el-select v-model="searchForm.type" placeholder="请选择权限类型" clearable>
              <el-option label="菜单权限" value="menu"></el-option>
              <el-option label="按钮权限" value="button"></el-option>
              <el-option label="接口权限" value="api"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleAdd">新增权限</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="权限名称"></el-table-column>
        <el-table-column prop="code" label="权限编码"></el-table-column>
        <el-table-column prop="type" label="权限类型" width="100">
          <template slot-scope="scope">
            <el-tag :type="getTypeColor(scope.row.type)">
              {{ getTypeName(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="resource" label="资源路径"></el-table-column>
        <el-table-column prop="method" label="请求方法" width="100">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.method" :type="getMethodColor(scope.row.method)">
              {{ scope.row.method }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="description" label="描述"></el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间"></el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        style="margin-top: 20px; text-align: right;">
      </el-pagination>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="权限名称" prop="name">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="权限编码" prop="code">
          <el-input v-model="form.code" :disabled="isEdit"></el-input>
        </el-form-item>
        <el-form-item label="权限类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择权限类型">
            <el-option label="菜单权限" value="menu"></el-option>
            <el-option label="按钮权限" value="button"></el-option>
            <el-option label="接口权限" value="api"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="资源路径" prop="resource">
          <el-input v-model="form.resource" placeholder="如：/api/users 或 /system/user"></el-input>
        </el-form-item>
        <el-form-item label="请求方法" prop="method" v-if="form.type === 'api'">
          <el-select v-model="form.method" placeholder="请选择请求方法">
            <el-option label="GET" value="GET"></el-option>
            <el-option label="POST" value="POST"></el-option>
            <el-option label="PUT" value="PUT"></el-option>
            <el-option label="DELETE" value="DELETE"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" rows="3"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from '@/api'
import permissionMixin from '@/mixins/permission'

export default {
  name: 'Permission',
  mixins: [permissionMixin],
  data() {
    return {
      loading: false,
      searchForm: {
        name: '',
        type: ''
      },
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      dialogVisible: false,
      isEdit: false,
      form: {
        id: null,
        name: '',
        code: '',
        type: 'menu',
        resource: '',
        method: '',
        description: '',
        status: 1
      },
      rules: {
        name: [
          { required: true, message: '请输入权限名称', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入权限编码', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择权限类型', trigger: 'change' }
        ],
        resource: [
          { required: true, message: '请输入资源路径', trigger: 'blur' }
        ]
      }
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑权限' : '新增权限'
    }
  },
  methods: {
    getTypeName(type) {
      const typeMap = {
        menu: '菜单权限',
        button: '按钮权限',
        api: '接口权限'
      }
      return typeMap[type] || type
    },
    getTypeColor(type) {
      const colorMap = {
        menu: 'primary',
        button: 'success',
        api: 'warning'
      }
      return colorMap[type] || ''
    },
    getMethodColor(method) {
      const colorMap = {
        GET: 'success',
        POST: 'primary',
        PUT: 'warning',
        DELETE: 'danger'
      }
      return colorMap[method] || ''
    },
    async handleSearch() {
      this.pagination.currentPage = 1
      await this.loadData()
    },
    handleReset() {
      this.searchForm = {
        name: '',
        type: ''
      }
      this.loadData()
    },
    handleAdd() {
      this.isEdit = false
      this.form = {
        id: null,
        name: '',
        code: '',
        type: 'menu',
        resource: '',
        method: '',
        description: '',
        status: 1
      }
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.isEdit = true
      this.form = { ...row }
      this.dialogVisible = true
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除该权限吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await api.permissions.delete(row.id)
        this.$message.success('删除成功')
        await this.loadData()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },
    async handleSubmit() {
      try {
        const valid = await this.$refs.form.validate()
        if (!valid) return

        if (this.isEdit) {
          await api.permissions.update(this.form.id, this.form)
          this.$message.success('编辑成功')
        } else {
          await api.permissions.create(this.form)
          this.$message.success('新增成功')
        }

        this.dialogVisible = false
        await this.loadData()
      } catch (error) {
        this.$message.error(this.isEdit ? '编辑失败' : '新增失败')
      }
    },
    async handleSizeChange(val) {
      this.pagination.pageSize = val
      await this.loadData()
    },
    async handleCurrentChange(val) {
      this.pagination.currentPage = val
      await this.loadData()
    },
    async loadData() {
      try {
        this.loading = true
        const params = {
          page: this.pagination.currentPage,
          pageSize: this.pagination.pageSize,
          ...this.searchForm
        }

        const response = await api.permissions.list(params)
        this.tableData = response.data || []
        // 权限管理通常不分页，显示所有权限
        this.pagination.total = this.tableData.length
      } catch (error) {
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    formatDate(date) {
      if (!date) return '-'
      return new Date(date).toLocaleString('zh-CN')
    }
  },
  async mounted() {
    await this.loadData()
  }
}
</script>

<style lang="scss" scoped>
.permission-management {
  .search-area {
    margin-bottom: 20px;

    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
}
</style>
