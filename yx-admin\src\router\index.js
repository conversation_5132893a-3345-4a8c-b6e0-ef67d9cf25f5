import Vue from 'vue'
import VueRouter from 'vue-router'
import store from '@/store'
import Login from '@/views/Login.vue'
import Layout from '@/layout/index.vue'

Vue.use(VueRouter)

// 常量路由（包含所有路由，通过权限控制显示）
export const constantRoutes = [
  {
    path: '/login',
    name: 'Login',
    component: Login,
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '首页', icon: 'el-icon-s-home' }
      }
    ]
  },
  {
    path: '/system',
    component: Layout,
    name: 'System',
    meta: { title: '系统管理', icon: 'el-icon-setting', permission: 'system' },
    children: [
      {
        path: 'user',
        name: 'User',
        component: () => import('@/views/system/User.vue'),
        meta: { title: '用户管理', icon: 'el-icon-user', permission: 'system:user' }
      },
      {
        path: 'role',
        name: 'Role',
        component: () => import('@/views/system/Role.vue'),
        meta: { title: '角色管理', icon: 'el-icon-s-custom', permission: 'system:role' }
      },
      {
        path: 'menu',
        name: 'Menu',
        component: () => import('@/views/system/Menu.vue'),
        meta: { title: '菜单管理', icon: 'el-icon-menu', permission: 'system:menu' }
      },
      {
        path: 'permission',
        name: 'Permission',
        component: () => import('@/views/system/Permission.vue'),
        meta: { title: '权限管理', icon: 'el-icon-key', permission: 'system:permission' }
      },
      {
        path: 'log',
        name: 'Log',
        component: () => import('@/views/system/Log.vue'),
        meta: { title: '系统日志', icon: 'el-icon-document', permission: 'system:log' }
      },
      {
        path: 'subject',
        name: 'Subject',
        component: () => import('@/views/system/Subject.vue'),
        meta: { title: '学科管理', icon: 'el-icon-collection', permission: 'system:subject' }
      }
    ]
  },
  {
    path: '/question',
    component: Layout,
    name: 'Question',
    meta: { title: '题库管理', icon: 'el-icon-notebook-2', permission: 'question' },
    children: [
      {
        path: 'bank',
        name: 'QuestionBank',
        component: () => import('@/views/question/Bank.vue'),
        meta: { title: '题库管理', icon: 'el-icon-folder', permission: 'question:bank' }
      },
      {
        path: 'book',
        name: 'Book',
        component: () => import('@/views/question/Book.vue'),
        meta: { title: '书籍管理', icon: 'el-icon-reading', permission: 'question:book' }
      },
      {
        path: 'chapter',
        name: 'Chapter',
        component: () => import('@/views/question/Chapter.vue'),
        meta: { title: '章节管理', icon: 'el-icon-menu', permission: 'question:chapter' }
      },
      {
        path: 'category',
        name: 'QuestionCategory',
        component: () => import('@/views/question/Category.vue'),
        meta: { title: '题目分类', icon: 'el-icon-files', permission: 'question:category' }
      },
      {
        path: 'list',
        name: 'QuestionList',
        component: () => import('@/views/question/List.vue'),
        meta: { title: '题目列表', icon: 'el-icon-document-copy', permission: 'question:list' }
      }
    ]
  },
  {
    path: '/404',
    component: () => import('@/views/404.vue'),
    hidden: true
  },
  // 404页面必须放在最后
  { path: '*', redirect: '/404', hidden: true }
]

// 导出异步路由（为了兼容性保留）
export const asyncRoutes = []

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes: constantRoutes
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  // 获取token
  const token = store.getters['user/token']

  // 白名单路由，不需要权限验证
  const whiteList = ['/login', '/404']

  if (whiteList.includes(to.path)) {
    // 如果是登录页面且已经有token，重定向到首页
    if (to.path === '/login' && token) {
      next('/')
    } else {
      next()
    }
    return
  }

  // 没有token，重定向到登录页
  if (!token) {
    next('/login')
    return
  }

  // 有token，直接通过（简化处理）
  next()
})

export default router
