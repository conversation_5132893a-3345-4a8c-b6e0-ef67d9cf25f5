<!--pages/statistics/statistics.wxml-->
<view class="container">
  <!-- 总体统计 -->
  <view class="stats-overview">
    <view class="stats-card">
      <view class="stats-number">{{ statistics.total }}</view>
      <view class="stats-label">总练习题数</view>
    </view>
    <view class="stats-card">
      <view class="stats-number">{{ statistics.correct }}</view>
      <view class="stats-label">答对题数</view>
    </view>
    <view class="stats-card">
      <view class="stats-number">{{ statistics.correctRate }}%</view>
      <view class="stats-label">正确率</view>
    </view>
  </view>

  <!-- 正确率进度 -->
  <view class="progress-section">
    <view class="section-title">学习进度</view>
    <view class="progress-card">
      <view class="progress-info">
        <view class="progress-text">
          <text>总体正确率</text>
          <text class="rate">{{ statistics.correctRate }}%</text>
        </view>
        <view class="progress-bar">
          <view 
            class="progress-inner" 
            style="width: {{ statistics.correctRate }}%; background-color: #07c160;"
          ></view>
        </view>
      </view>
      <view class="progress-detail">
        <view class="detail-item">
          <text class="label">已练习：</text>
          <text class="value">{{ statistics.total }}题</text>
        </view>
        <view class="detail-item">
          <text class="label">答对：</text>
          <text class="value correct">{{ statistics.correct }}题</text>
        </view>
        <view class="detail-item">
          <text class="label">答错：</text>
          <text class="value wrong">{{ statistics.wrong }}题</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 最近练习记录 -->
  <view class="recent-records" wx:if="{{ recentRecords.length > 0 }}">
    <view class="section-title">最近练习</view>
    <view class="records-list">
      <view 
        class="record-item" 
        wx:for="{{ recentRecords }}" 
        wx:key="index"
      >
        <view class="record-info">
          <view class="record-question">题目 #{{ item.questionId }}</view>
          <view class="record-time">{{ formatTime(item.timestamp) }}</view>
        </view>
        <view class="record-result">
          <view class="tag {{ item.isCorrect ? 'tag-success' : 'tag-danger' }}">
            {{ item.isCorrect ? '正确' : '错误' }}
          </view>
        </view>
      </view>
    </view>
  </view>

  <!-- 学习建议 */
  <view class="suggestions">
    <view class="section-title">学习建议</view>
    <view class="suggestion-card">
      <view class="suggestion-item" wx:if="{{ statistics.correctRate < 60 }}">
        <image src="/images/warning-o.png" class="suggestion-icon warning" />
        <text>正确率偏低，建议加强基础知识学习</text>
      </view>
      <view class="suggestion-item" wx:elif="{{ statistics.correctRate < 80 }}">
        <image src="/images/info-o.png" class="suggestion-icon info" />
        <text>正确率良好，继续保持练习节奏</text>
      </view>
      <view class="suggestion-item" wx:else>
        <image src="/images/success.png" class="suggestion-icon success" />
        <text>正确率优秀，可以尝试更有挑战性的题目</text>
      </view>
      
      <view class="suggestion-item" wx:if="{{ statistics.total < 10 }}">
        <image src="/images/clock-o.png" class="suggestion-icon clock" />
        <text>练习题数较少，建议增加练习量</text>
      </view>
      
      <view class="suggestion-item" wx:if="{{ statistics.total > 0 }}">
        <image src="/images/star-o.png" class="suggestion-icon star" />
        <text>坚持每日练习，养成良好的学习习惯</text>
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{ statistics.total === 0 }}">
    <image src="/images/chart-trending-o.png" class="empty-icon" />
    <view class="empty-text">暂无练习数据</view>
    <button class="btn-primary btn-small" bindtap="goToPractice">
      开始练习
    </button>
  </view>
</view>
