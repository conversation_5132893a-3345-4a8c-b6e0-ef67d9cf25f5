// utils/dialog.js
// 替代 Vant Dialog 的原生实现

const Dialog = {
  // 显示确认对话框
  confirm(options) {
    return new Promise((resolve, reject) => {
      wx.showModal({
        title: options.title || '提示',
        content: options.message || '',
        confirmText: options.confirmButtonText || '确定',
        cancelText: options.cancelButtonText || '取消',
        success(res) {
          if (res.confirm) {
            resolve(true)
          } else {
            resolve(false)
          }
        },
        fail(err) {
          reject(err)
        }
      })
    })
  },

  // 显示提示对话框（只有确认按钮）
  alert(options) {
    return new Promise((resolve, reject) => {
      wx.showModal({
        title: options.title || '提示',
        content: options.message || '',
        confirmText: options.confirmButtonText || '确定',
        showCancel: false,
        success(res) {
          if (res.confirm) {
            resolve(true)
          }
        },
        fail(err) {
          reject(err)
        }
      })
    })
  }
}

module.exports = Dialog