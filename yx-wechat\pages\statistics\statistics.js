// pages/statistics/statistics.js
const auth = require('../../utils/auth.js')
const storage = require('../../utils/storage.js')
const Toast = require('../../utils/toast.js')

Page({
  data: {
    statistics: {
      total: 0,
      correct: 0,
      wrong: 0,
      correctRate: 0
    },
    recentRecords: []
  },

  onLoad() {
    this.checkLoginAndLoadData()
  },

  onShow() {
    this.checkLoginAndLoadData()
  },

  // 检查登录状态并加载数据
  checkLoginAndLoadData() {
    if (!auth.requireLogin()) return
    this.loadStatistics()
    this.loadRecentRecords()
  },

  // 加载统计数据
  loadStatistics() {
    const statistics = storage.progress.getStatistics()
    this.setData({ statistics })
  },

  // 加载最近练习记录
  loadRecentRecords() {
    const allRecords = storage.progress.getAnswerRecords()
    // 取最近20条记录
    const recentRecords = allRecords
      .sort((a, b) => b.timestamp - a.timestamp)
      .slice(0, 20)
    
    this.setData({ recentRecords })
  },

  // 格式化时间
  formatTime(timestamp) {
    const date = new Date(timestamp)
    const now = new Date()
    const diff = now - date
    
    // 小于1分钟
    if (diff < 60000) {
      return '刚刚'
    }
    
    // 小于1小时
    if (diff < 3600000) {
      const minutes = Math.floor(diff / 60000)
      return `${minutes}分钟前`
    }
    
    // 小于1天
    if (diff < 86400000) {
      const hours = Math.floor(diff / 3600000)
      return `${hours}小时前`
    }
    
    // 小于7天
    if (diff < 604800000) {
      const days = Math.floor(diff / 86400000)
      return `${days}天前`
    }
    
    // 超过7天显示具体日期
    const month = date.getMonth() + 1
    const day = date.getDate()
    return `${month}月${day}日`
  },

  // 跳转到练习页面
  goToPractice() {
    wx.switchTab({
      url: '/pages/subjects/subjects'
    })
  },

  // 下拉刷新
  onPullDownRefresh() {
    this.loadStatistics()
    this.loadRecentRecords()
    wx.stopPullDownRefresh()
  }
})
