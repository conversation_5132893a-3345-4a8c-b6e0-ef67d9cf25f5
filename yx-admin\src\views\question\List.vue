<template>
  <div class="question-list">
    <el-card>
      <!-- 面包屑导航 -->
      <div class="breadcrumb-area" v-if="currentChapter">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/question/bank' }">题库管理</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/question/book', query: { bankId: bankId, bankName: currentBank } }">
            {{ currentBank }}
          </el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/question/chapter', query: { bankId: bankId, bankName: currentBank, bookId: bookId, bookName: currentBook } }">
            {{ currentBook }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>{{ currentChapter }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="题目内容">
            <el-input v-model="searchForm.content" placeholder="请输入题目内容" clearable style="width: 200px;"></el-input>
          </el-form-item>
          <el-form-item label="题目类型">
            <el-select v-model="searchForm.type" placeholder="请选择题目类型" clearable>
              <el-option label="单选题" value="single"></el-option>
              <el-option label="多选题" value="multiple"></el-option>
              <el-option label="判断题" value="judge"></el-option>
              <el-option label="填空题" value="fill"></el-option>
              <el-option label="简答题" value="essay"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="难度">
            <el-select v-model="searchForm.difficulty" placeholder="请选择难度" clearable>
              <el-option label="简单" value="easy"></el-option>
              <el-option label="中等" value="medium"></el-option>
              <el-option label="困难" value="hard"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="启用" value="1"></el-option>
              <el-option label="禁用" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleAdd">新增题目</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="content" label="题目内容" show-overflow-tooltip width="300">
          <template slot-scope="scope">
            <div v-html="scope.row.content"></div>
          </template>
        </el-table-column>
        <el-table-column prop="type" label="题目类型" width="100">
          <template slot-scope="scope">
            <el-tag :type="getQuestionTypeColor(scope.row.type)">
              {{ getQuestionTypeName(scope.row.type) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="difficulty" label="难度" width="80">
          <template slot-scope="scope">
            <el-tag :type="getDifficultyColor(scope.row.difficulty)">
              {{ getDifficultyName(scope.row.difficulty) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="categoryName" label="分类" width="120">
          <template slot-scope="scope">
            <el-tag type="info">{{ scope.row.categoryName }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="score" label="分值" width="80">
          <template slot-scope="scope">
            <el-tag type="warning">{{ scope.row.score }}分</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160"></el-table-column>
        <el-table-column label="操作" width="250">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleView(scope.row)">查看</el-button>
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        style="margin-top: 20px; text-align: right;">
      </el-pagination>
    </el-card>

    <!-- 查看题目对话框 -->
    <el-dialog title="题目详情" :visible.sync="viewDialogVisible" width="800px">
      <div class="question-detail" v-if="currentQuestion">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="题目类型">
            <el-tag :type="getQuestionTypeColor(currentQuestion.type)">
              {{ getQuestionTypeName(currentQuestion.type) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="难度等级">
            <el-tag :type="getDifficultyColor(currentQuestion.difficulty)">
              {{ getDifficultyName(currentQuestion.difficulty) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="分类">{{ currentQuestion.categoryName }}</el-descriptions-item>
          <el-descriptions-item label="分值">{{ currentQuestion.score }}分</el-descriptions-item>
          <el-descriptions-item label="题目内容" :span="2">
            <div v-html="currentQuestion.content"></div>
          </el-descriptions-item>
          <el-descriptions-item label="选项" :span="2" v-if="currentQuestion.type === 'single' || currentQuestion.type === 'multiple'">
            <div v-for="(option, index) in currentQuestion.options" :key="index" style="margin-bottom: 8px;">
              <span :style="{ color: currentQuestion.correctAnswer.includes(String.fromCharCode(65 + index)) ? '#67C23A' : '#606266' }">
                {{ String.fromCharCode(65 + index) }}. {{ option }}
                <i class="el-icon-check" v-if="currentQuestion.correctAnswer.includes(String.fromCharCode(65 + index))" style="color: #67C23A; margin-left: 8px;"></i>
              </span>
            </div>
          </el-descriptions-item>
          <el-descriptions-item label="正确答案" :span="2">
            <el-tag type="success">{{ currentQuestion.correctAnswer }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="解析" :span="2" v-if="currentQuestion.explanation">
            <div v-html="currentQuestion.explanation"></div>
          </el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>
    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="900px">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="题目类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择题目类型" @change="handleTypeChange">
            <el-option label="单选题" value="single"></el-option>
            <el-option label="多选题" value="multiple"></el-option>
            <el-option label="判断题" value="judge"></el-option>
            <el-option label="简答题" value="essay"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="题目内容" prop="content">
          <el-input v-model="form.content" type="textarea" rows="4" placeholder="请输入题目内容"></el-input>
        </el-form-item>
        <el-form-item label="选项" v-if="form.type === 'single' || form.type === 'multiple'">
          <div v-for="(option, index) in form.options" :key="index" style="margin-bottom: 8px;">
            <el-input v-model="form.options[index]" :placeholder="`选项${String.fromCharCode(65 + index)}`">
              <template slot="prepend">{{ String.fromCharCode(65 + index) }}</template>
              <template slot="append">
                <el-button @click="removeOption(index)" v-if="form.options.length > 2">删除</el-button>
              </template>
            </el-input>
          </div>
          <el-button @click="addOption" v-if="form.options.length < 6" style="margin-top: 8px;">添加选项</el-button>
        </el-form-item>
        <el-form-item label="正确答案" prop="correctAnswer">
          <el-input v-model="form.correctAnswer" placeholder="请输入正确答案"></el-input>
          <div style="font-size: 12px; color: #999; margin-top: 4px;">
            <span v-if="form.type === 'single'">单选题请输入选项字母，如：A</span>
            <span v-else-if="form.type === 'multiple'">多选题请输入选项字母，如：AB</span>
            <span v-else-if="form.type === 'judge'">判断题请输入：正确 或 错误</span>
            <span v-else>请输入标准答案</span>
          </div>
        </el-form-item>
        <el-form-item label="解析" prop="explanation">
          <el-input v-model="form.explanation" type="textarea" rows="3" placeholder="请输入题目解析（可选）"></el-input>
        </el-form-item>
        <el-form-item label="难度等级" prop="difficulty">
          <el-select v-model="form.difficulty" placeholder="请选择难度等级">
            <el-option label="简单" :value="1"></el-option>
            <el-option label="中等" :value="2"></el-option>
            <el-option label="困难" :value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="题目分类" prop="categoryId">
          <el-select v-model="form.categoryId" placeholder="请选择题目分类">
            <el-option
              v-for="category in categoryList"
              :key="category.id"
              :label="category.name"
              :value="category.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="分值" prop="score">
          <el-input-number v-model="form.score" :min="1" :max="100"></el-input-number>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'QuestionList',
  data() {
    return {
      loading: false,
      currentBank: '',
      currentBook: '',
      currentChapter: '',
      bankId: null,
      bookId: null,
      chapterId: null,
      searchForm: {
        content: '',
        type: '',
        difficulty: '',
        status: ''
      },
      tableData: [
        {
          id: 1,
          content: '1 + 1 = ?',
          type: 'single',
          difficulty: 'easy',
          categoryId: 1,
          categoryName: '基础计算',
          score: 5,
          status: 1,
          options: ['1', '2', '3', '4'],
          correctAnswer: 'B',
          explanation: '1加1等于2',
          createTime: '2024-01-01 10:00:00'
        },
        {
          id: 2,
          content: '下列哪些是偶数？',
          type: 'multiple',
          difficulty: 'easy',
          categoryId: 1,
          categoryName: '基础计算',
          score: 8,
          status: 1,
          options: ['2', '3', '4', '5'],
          correctAnswer: 'AC',
          explanation: '偶数是能被2整除的数，2和4都是偶数',
          createTime: '2024-01-01 10:00:00'
        },
        {
          id: 3,
          content: '0是自然数。',
          type: 'judge',
          difficulty: 'medium',
          categoryId: 3,
          categoryName: '选择判断',
          score: 3,
          status: 1,
          options: [],
          correctAnswer: '正确',
          explanation: '根据现代数学定义，0是自然数',
          createTime: '2024-01-01 10:00:00'
        }
      ],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 3
      },
      viewDialogVisible: false,
      dialogVisible: false,
      isEdit: false,
      currentQuestion: null,
      form: {
        id: null,
        content: '',
        type: '',
        options: ['', ''],
        correctAnswer: '',
        explanation: '',
        difficulty: '',
        categoryId: '',
        score: 5,
        status: 1
      },
      rules: {
        content: [
          { required: true, message: '请输入题目内容', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择题目类型', trigger: 'change' }
        ],
        correctAnswer: [
          { required: true, message: '请输入正确答案', trigger: 'blur' }
        ],
        difficulty: [
          { required: true, message: '请选择难度等级', trigger: 'change' }
        ],
        categoryId: [
          { required: true, message: '请选择题目分类', trigger: 'change' }
        ]
      },
      categoryList: [
        { id: 1, name: '基础计算' },
        { id: 2, name: '应用题' },
        { id: 3, name: '选择判断' },
        { id: 4, name: '填空练习' }
      ]
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑题目' : '新增题目'
    }
  },
  methods: {
    getQuestionTypeName(type) {
      const typeMap = {
        single: '单选题',
        multiple: '多选题',
        judge: '判断题',
        fill: '填空题',
        essay: '简答题'
      }
      return typeMap[type] || type
    },
    getQuestionTypeColor(type) {
      const colorMap = {
        single: 'primary',
        multiple: 'success',
        judge: 'warning',
        fill: 'info',
        essay: 'danger'
      }
      return colorMap[type] || ''
    },
    getDifficultyName(difficulty) {
      const difficultyMap = {
        1: '简单',
        2: '中等',
        3: '困难',
        easy: '简单',
        medium: '中等',
        hard: '困难'
      }
      return difficultyMap[difficulty] || difficulty
    },
    getDifficultyColor(difficulty) {
      const colorMap = {
        1: 'success',
        2: 'warning',
        3: 'danger',
        easy: 'success',
        medium: 'warning',
        hard: 'danger'
      }
      return colorMap[difficulty] || ''
    },
    handleSearch() {
      this.pagination.currentPage = 1
      this.loadData()
    },
    handleReset() {
      this.searchForm = {
        content: '',
        type: '',
        difficulty: '',
        status: ''
      }
      this.loadData()
    },
    handleAdd() {
      this.isEdit = false
      this.form = {
        id: null,
        content: '',
        type: '',
        options: ['', ''],
        correctAnswer: '',
        explanation: '',
        difficulty: '',
        categoryId: '',
        score: 5,
        status: 1
      }
      this.dialogVisible = true
    },
    handleView(row) {
      this.currentQuestion = row
      this.viewDialogVisible = true
    },
    handleEdit(row) {
      this.isEdit = true
      this.form = { ...row }
      this.dialogVisible = true
    },
    handleDelete(row) {
      this.$confirm('确定要删除该题目吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
        this.loadData()
      })
    },
    handleTypeChange(type) {
      if (type === 'single' || type === 'multiple') {
        this.form.options = ['', '']
      } else {
        this.form.options = []
      }
      this.form.correctAnswer = ''
    },
    addOption() {
      this.form.options.push('')
    },
    removeOption(index) {
      this.form.options.splice(index, 1)
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$message.success(this.isEdit ? '编辑成功' : '新增成功')
          this.dialogVisible = false
          this.loadData()
        }
      })
    },
    handleSizeChange(val) {
      this.pagination.pageSize = val
      this.loadData()
    },
    handleCurrentChange(val) {
      this.pagination.currentPage = val
      this.loadData()
    },
    loadData() {
      this.loading = true
      // 模拟API调用
      setTimeout(() => {
        this.loading = false
      }, 500)
    }
  },
  mounted() {
    // 获取路由参数
    this.bankId = this.$route.query.bankId
    this.currentBank = this.$route.query.bankName
    this.bookId = this.$route.query.bookId
    this.currentBook = this.$route.query.bookName
    this.chapterId = this.$route.query.chapterId
    this.currentChapter = this.$route.query.chapterName
    this.loadData()
  }
}
</script>

<style lang="scss" scoped>
.question-list {
  .breadcrumb-area {
    margin-bottom: 20px;
  }

  .search-area {
    margin-bottom: 20px;

    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }

  .question-detail {
    .el-descriptions {
      margin-top: 20px;
    }
  }
}
</style>
