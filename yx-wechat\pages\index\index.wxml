<!--pages/index/index.wxml-->
<view class="container">
  <!-- 用户信息卡片 -->
  <view class="user-card" wx:if="{{ userInfo }}">
    <view class="user-info">
      <view class="avatar">
        <text class="icon-user">👤</text>
      </view>
      <view class="info">
        <view class="name">{{ userInfo.username }}</view>
        <view class="desc">继续加油学习吧！</view>
      </view>
    </view>
    <view class="stats">
      <view class="stat-item">
        <view class="number">{{ statistics.total }}</view>
        <view class="label">已练习</view>
      </view>
      <view class="stat-item">
        <view class="number">{{ statistics.correct }}</view>
        <view class="label">答对题</view>
      </view>
      <view class="stat-item">
        <view class="number">{{ statistics.correctRate }}%</view>
        <view class="label">正确率</view>
      </view>
    </view>
  </view>

  <!-- 未登录状态 -->
  <view class="login-prompt" wx:else>
    <text class="icon-user-large">👤</text>
    <view class="prompt-text">请先登录</view>
    <button class="login-btn" bind:tap="goToLogin">
      立即登录
    </button>
  </view>

  <!-- 快速入口 -->
  <view class="quick-actions">
    <view class="section-title">快速入口</view>
    <view class="action-grid">
      <view class="action-item" bind:tap="goToSubjects">
        <text class="action-icon">📚</text>
        <text class="action-text">开始学习</text>
      </view>
      <view class="action-item" bind:tap="goToStatistics">
        <text class="action-icon">📊</text>
        <text class="action-text">学习统计</text>
      </view>
    </view>
  </view>

  <!-- 最近学习 -->
  <view class="recent-study" wx:if="{{ recentSubjects.length > 0 }}">
    <view class="section-title">最近学习</view>
    <view class="subject-list">
      <view
        class="subject-item"
        wx:for="{{ recentSubjects }}"
        wx:key="id"
        bind:tap="goToQuestionBanks"
        data-subject="{{ item }}"
      >
        <view class="subject-info">
          <view class="subject-name">{{ item.name }}</view>
          <view class="subject-desc">{{ item.description }}</view>
        </view>
        <text class="arrow-icon">→</text>
      </view>
    </view>
  </view>

  <!-- 推荐学科 -->
  <view class="recommended" wx:if="{{ recommendedSubjects.length > 0 }}">
    <view class="section-title">推荐学科</view>
    <view class="subject-grid">
      <view
        class="subject-card"
        wx:for="{{ recommendedSubjects }}"
        wx:key="id"
        bind:tap="goToQuestionBanks"
        data-subject="{{ item }}"
      >
        <view class="card-icon" style="background-color: {{ item.color || '#1989fa' }}">
          <text class="card-emoji">📖</text>
        </view>
        <view class="card-name">{{ item.name }}</view>
      </view>
    </view>
  </view>
</view>
