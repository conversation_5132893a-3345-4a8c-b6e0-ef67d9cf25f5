const Router = require('koa-router');
const { response } = require('../utils/response');
const { query } = require('../config/database');
const { authMiddleware, permissionMiddleware } = require('../middleware/auth');

const router = new Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取系统日志列表
router.get('/', permissionMiddleware('system:log'), async (ctx) => {
  const { page = 1, pageSize = 10 } = ctx.query;

  // 暂时返回空数据，等待系统日志表结构确认
  const logs = [];
  const total = 0;

  ctx.body = response.page(logs, total, page, pageSize);
});

// 创建系统日志（内部使用）
router.post('/', async (ctx) => {
  const { type, module, action, description, ip, userAgent, userId } = ctx.request.body;

  await query(
    'INSERT INTO system_logs (type, module, action, description, ip, user_agent, user_id, created_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
    [type, module, action, description, ip, userAgent, userId, new Date()]
  );

  ctx.body = response.success(null, '日志记录成功');
});

module.exports = router;
