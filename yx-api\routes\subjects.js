const Router = require('koa-router');
const { response } = require('../utils/response');
const { validation } = require('../utils/validation');
const { query } = require('../config/database');
const { authMiddleware, permissionMiddleware } = require('../middleware/auth');

const router = new Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取学科列表
router.get('/', permissionMiddleware('system:subject'), async (ctx) => {
  const { page = 1, pageSize = 10, name, status } = ctx.query;

  let whereClause = 'WHERE 1=1';
  const params = [];

  if (name) {
    whereClause += ' AND name LIKE ?';
    params.push(`%${name}%`);
  }

  if (status !== undefined) {
    whereClause += ' AND status = ?';
    params.push(status);
  }

  // 查询总数
  const countSql = `SELECT COUNT(*) as total FROM subjects ${whereClause}`;
  const countResult = await query(countSql, params);
  const total = countResult[0].total;

  // 查询数据
  const offset = (page - 1) * pageSize;
  const dataSql = `
    SELECT id, name, code, icon, color, description, sort, status, created_time, updated_time
    FROM subjects
    ${whereClause}
    ORDER BY sort ASC, created_time DESC
    LIMIT ${parseInt(pageSize)} OFFSET ${parseInt(offset)}
  `;

  const subjects = await query(dataSql, params);

  ctx.body = response.page(subjects, total, page, pageSize);
});

// 获取所有启用的学科（不分页）
router.get('/all', async (ctx) => {
  const subjects = await query(
    'SELECT id, name, code, icon, color, description, sort FROM subjects WHERE status = 1 ORDER BY sort ASC, created_time DESC'
  );

  ctx.body = response.success(subjects);
});

// 获取学科详情
router.get('/:id', permissionMiddleware('system:subject'), async (ctx) => {
  const { id } = ctx.params;

  const subjects = await query(
    'SELECT id, name, code, icon, color, description, sort, status, created_time, updated_time FROM subjects WHERE id = ?',
    [id]
  );

  if (subjects.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('学科不存在', 404);
    return;
  }

  ctx.body = response.success(subjects[0]);
});

// 创建学科
router.post('/', permissionMiddleware('system:subject'), async (ctx) => {
  const { name, code, icon, color, description, sort = 0 } = ctx.request.body;

  // 参数验证
  validation.required(name, '学科名称');
  validation.required(code, '学科编码');
  validation.length(name, 1, 50, '学科名称');
  validation.length(code, 1, 50, '学科编码');

  // 检查编码是否已存在
  const existingSubjects = await query(
    'SELECT id FROM subjects WHERE code = ?',
    [code]
  );

  if (existingSubjects.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('学科编码已存在', 400);
    return;
  }

  // 创建学科
  const result = await query(
    'INSERT INTO subjects (name, code, icon, color, description, sort, status, created_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?)',
    [name, code, icon || null, color || null, description || null, sort, 1, new Date()]
  );

  ctx.body = response.success({ id: result.insertId, name, code }, '学科创建成功');
});

// 更新学科
router.put('/:id', permissionMiddleware('system:subject'), async (ctx) => {
  const { id } = ctx.params;
  const { name, code, icon, color, description, sort, status } = ctx.request.body;

  // 参数验证
  validation.required(name, '学科名称');
  validation.required(code, '学科编码');
  validation.length(name, 1, 50, '学科名称');
  validation.length(code, 1, 50, '学科编码');

  // 检查学科是否存在
  const existingSubjects = await query(
    'SELECT id FROM subjects WHERE id = ?',
    [id]
  );

  if (existingSubjects.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('学科不存在', 404);
    return;
  }

  // 检查编码是否被其他学科使用
  const duplicateSubjects = await query(
    'SELECT id FROM subjects WHERE code = ? AND id != ?',
    [code, id]
  );

  if (duplicateSubjects.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('学科编码已被其他学科使用', 400);
    return;
  }

  // 更新学科
  await query(
    'UPDATE subjects SET name = ?, code = ?, icon = ?, color = ?, description = ?, sort = ?, status = ?, updated_time = ? WHERE id = ?',
    [name, code, icon || null, color || null, description || null, sort, status, new Date(), id]
  );

  ctx.body = response.success(null, '学科更新成功');
});

// 删除学科
router.delete('/:id', permissionMiddleware('system:subject'), async (ctx) => {
  const { id } = ctx.params;

  // 检查学科是否存在
  const existingSubjects = await query(
    'SELECT id FROM subjects WHERE id = ?',
    [id]
  );

  if (existingSubjects.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('学科不存在', 404);
    return;
  }

  // 检查是否有关联的题库
  const questionBanks = await query(
    'SELECT id FROM question_banks WHERE subject_id = ?',
    [id]
  );

  if (questionBanks.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('该学科下存在题库，无法删除', 400);
    return;
  }

  // 删除学科
  await query('DELETE FROM subjects WHERE id = ?', [id]);

  ctx.body = response.success(null, '学科删除成功');
});

// 批量更新排序
router.put('/sort/batch', permissionMiddleware('system:subject'), async (ctx) => {
  const { subjects } = ctx.request.body;

  validation.isArray(subjects, '学科列表');

  if (subjects.length === 0) {
    ctx.status = 400;
    ctx.body = response.error('学科列表不能为空', 400);
    return;
  }

  // 批量更新排序
  for (let subject of subjects) {
    await query(
      'UPDATE subjects SET sort = ?, updated_time = ? WHERE id = ?',
      [subject.sort, new Date(), subject.id]
    );
  }

  ctx.body = response.success(null, '排序更新成功');
});

module.exports = router;
