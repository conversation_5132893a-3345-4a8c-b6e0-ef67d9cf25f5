// utils/toast.js
// 替代 Vant Toast 的原生实现

const Toast = {
  // 显示文本提示
  text(message, options = {}) {
    wx.showToast({
      title: message,
      icon: 'none',
      duration: options.duration || 2000,
      mask: options.mask || false
    })
  },

  // 显示成功提示
  success(message, options = {}) {
    wx.showToast({
      title: message,
      icon: 'success',
      duration: options.duration || 2000,
      mask: options.mask || false
    })
  },

  // 显示失败提示
  fail(message, options = {}) {
    wx.showToast({
      title: message,
      icon: 'error',
      duration: options.duration || 2000,
      mask: options.mask || false
    })
  },

  // 显示加载提示
  loading(message, options = {}) {
    wx.showLoading({
      title: message || '加载中...',
      mask: options.mask || false
    })
  },

  // 清除提示
  clear() {
    wx.hideToast()
    wx.hideLoading()
  }
}

module.exports = Toast