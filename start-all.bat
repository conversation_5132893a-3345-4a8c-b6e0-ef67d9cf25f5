@echo off
chcp 65001 >nul

echo 🚀 启动YX刷题小程序系统...

REM 检查Node.js是否安装
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js 未安装，请先安装 Node.js
    pause
    exit /b 1
)

REM 检查npm是否安装
npm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm 未安装，请先安装 npm
    pause
    exit /b 1
)

echo 📦 安装依赖...

REM 安装后端依赖
echo 安装后端依赖...
cd yx-api
if not exist "node_modules" (
    npm install
)

REM 复制环境配置文件
if not exist ".env" (
    copy .env.example .env
    echo ✅ 已创建环境配置文件 .env，请根据需要修改数据库配置
)

REM 初始化数据库
echo 🗄️ 初始化数据库...
node scripts/initDatabase.js

REM 启动后端服务
echo 🔧 启动后端API服务...
start "YX-API" cmd /k "npm run dev"

cd ..

REM 安装前端依赖
echo 安装前端依赖...
cd yx-admin
if not exist "node_modules" (
    npm install
)

REM 启动前端服务
echo 🎨 启动前端管理系统...
start "YX-Admin" cmd /k "npm run serve"

cd ..

echo ✅ 系统启动完成！
echo.
echo 📋 服务信息：
echo    后端API服务: http://localhost:3000
echo    前端管理系统: http://localhost:8080
echo.
echo 🔑 默认登录账号：
echo    用户名: admin
echo    密码: 123456
echo.
echo 💡 提示：
echo    - 关闭对应的命令行窗口可停止服务
echo    - 微信小程序需要使用微信开发者工具打开 yx-wechat 目录
echo.

pause
