// pages/login/login.js
const auth = require('../../utils/auth.js')

Page({
  data: {
    username: '',
    password: '',
    loading: false
  },

  onLoad() {
    // 如果已经登录，直接跳转到首页
    if (auth.checkLogin()) {
      wx.switchTab({
        url: '/pages/index/index'
      })
    }
  },

  // 用户名输入
  onUsernameChange(event) {
    this.setData({
      username: event.detail.value
    })
  },

  // 密码输入
  onPasswordChange(event) {
    this.setData({
      password: event.detail.value
    })
  },

  // 登录
  onLogin() {
    const { username, password } = this.data

    if (!username.trim()) {
      wx.showToast({
        title: '请输入用户名',
        icon: 'none'
      })
      return
    }

    if (!password.trim()) {
      wx.showToast({
        title: '请输入密码',
        icon: 'none'
      })
      return
    }

    this.setData({ loading: true })

    auth.login(username, password)
      .then(() => {
        wx.showToast({
          title: '登录成功',
          icon: 'success'
        })
        setTimeout(() => {
          wx.switchTab({
            url: '/pages/index/index'
          })
        }, 1500)
      })
      .catch((err) => {
        console.error('登录失败:', err)
        wx.showToast({
          title: err.message || '登录失败',
          icon: 'none'
        })
      })
      .finally(() => {
        this.setData({ loading: false })
      })
  },

  // 跳转到注册页面
  goToRegister() {
    wx.showToast({
      title: '注册功能暂未开放',
      icon: 'none'
    })
  }
})
