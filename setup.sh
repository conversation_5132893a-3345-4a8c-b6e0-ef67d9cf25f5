#!/bin/bash

# YX刷题项目快速安装脚本
# 使用方法: bash setup.sh

echo "🚀 YX刷题项目快速安装脚本"
echo "================================"

# 检查Node.js版本
check_node() {
    echo "📋 检查Node.js环境..."
    if ! command -v node &> /dev/null; then
        echo "❌ 未检测到Node.js，请先安装Node.js >= 14.0.0"
        echo "   下载地址: https://nodejs.org/"
        exit 1
    fi
    
    NODE_VERSION=$(node -v | cut -d'v' -f2)
    REQUIRED_VERSION="14.0.0"
    
    if [ "$(printf '%s\n' "$REQUIRED_VERSION" "$NODE_VERSION" | sort -V | head -n1)" != "$REQUIRED_VERSION" ]; then
        echo "❌ Node.js版本过低，当前版本: $NODE_VERSION，要求版本: >= $REQUIRED_VERSION"
        exit 1
    fi
    
    echo "✅ Node.js版本检查通过: $NODE_VERSION"
}

# 检查npm版本
check_npm() {
    echo "📋 检查npm环境..."
    if ! command -v npm &> /dev/null; then
        echo "❌ 未检测到npm"
        exit 1
    fi
    
    NPM_VERSION=$(npm -v)
    echo "✅ npm版本: $NPM_VERSION"
}

# 安装依赖
install_dependencies() {
    echo ""
    echo "📦 开始安装项目依赖..."
    echo "================================"
    
    # 安装API项目依赖
    echo "🔧 安装yx-api依赖..."
    cd yx-api
    if [ -f ".npmrc" ]; then
        echo "   使用项目配置的npm镜像源"
    else
        echo "   配置npm镜像源为npmmirror.com..."
        echo "registry=https://registry.npmmirror.com/" > .npmrc
    fi
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ yx-api依赖安装失败"
        exit 1
    fi
    echo "✅ yx-api依赖安装完成"
    cd ..
    
    # 安装管理后台依赖
    echo "🔧 安装yx-admin依赖..."
    cd yx-admin
    if [ -f ".npmrc" ]; then
        echo "   使用项目配置的npm镜像源"
    else
        echo "   配置npm镜像源为npmmirror.com..."
        echo "registry=https://registry.npmmirror.com/" > .npmrc
    fi
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ yx-admin依赖安装失败"
        exit 1
    fi
    echo "✅ yx-admin依赖安装完成"
    cd ..
    
    # 安装微信小程序依赖
    echo "🔧 安装yx-wechat依赖..."
    cd yx-wechat
    npm install
    if [ $? -ne 0 ]; then
        echo "❌ yx-wechat依赖安装失败"
        exit 1
    fi
    echo "✅ yx-wechat依赖安装完成"
    cd ..
}

# 初始化数据库
init_database() {
    echo ""
    echo "🗄️ 初始化数据库..."
    echo "================================"
    echo "⚠️  请确保MySQL服务已启动，并且数据库配置正确"
    echo "   数据库配置信息："
    echo "   地址：*************"
    echo "   数据库名：yx-db"
    echo "   用户名：yx-db"
    echo "   密码：yDtxSFsdNMHTRbJw"
    echo ""
    read -p "是否继续初始化数据库？(y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        cd yx-api
        node scripts/initDatabase.js
        if [ $? -eq 0 ]; then
            echo "✅ 数据库初始化完成"
        else
            echo "❌ 数据库初始化失败，请检查数据库连接配置"
        fi
        cd ..
    else
        echo "⏭️  跳过数据库初始化"
    fi
}

# 显示启动说明
show_startup_guide() {
    echo ""
    echo "🎉 安装完成！"
    echo "================================"
    echo ""
    echo "📚 启动指南："
    echo ""
    echo "1. 启动后端API服务："
    echo "   cd yx-api"
    echo "   npm run dev"
    echo "   服务地址: http://localhost:3000"
    echo ""
    echo "2. 启动后台管理系统（新终端）："
    echo "   cd yx-admin"
    echo "   npm run serve"
    echo "   管理后台: http://localhost:8080"
    echo ""
    echo "3. 启动微信小程序："
    echo "   - 使用微信开发者工具导入 yx-wechat 目录"
    echo "   - 在微信开发者工具中执行"构建npm"操作"
    echo ""
    echo "🔑 默认管理员账号："
    echo "   用户名: admin"
    echo "   密码: 123456"
    echo ""
    echo "📖 更多信息请查看："
    echo "   - README.md - 项目说明"
    echo "   - 总需求.md - 完整需求文档"
    echo ""
    echo "🚀 快速启动命令："
    echo "   npm run dev:api     # 启动API服务"
    echo "   npm run dev:admin   # 启动管理后台"
    echo "   npm run init:db     # 初始化数据库"
    echo ""
}

# 主函数
main() {
    echo "开始安装YX刷题项目..."
    echo ""
    
    # 检查环境
    check_node
    check_npm
    
    # 安装依赖
    install_dependencies
    
    # 初始化数据库
    init_database
    
    # 显示启动说明
    show_startup_guide
    
    echo "✨ 安装脚本执行完成！"
}

# 执行主函数
main
