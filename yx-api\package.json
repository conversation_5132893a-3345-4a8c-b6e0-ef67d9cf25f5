{"name": "yx-api", "version": "1.0.0", "description": "YX刷题小程序API接口端", "main": "app.js", "scripts": {"start": "node app.js", "dev": "nodemon app.js", "test": "jest"}, "keywords": ["koa", "mysql", "api", "education"], "author": "", "license": "MIT", "dependencies": {"koa": "^2.14.2", "koa-router": "^12.0.0", "koa-bodyparser": "^4.4.1", "koa-cors": "^0.0.16", "koa-logger": "^3.2.1", "koa-json": "^2.0.2", "mysql2": "^3.6.5", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "dotenv": "^16.3.1", "moment": "^2.29.4"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0"}}