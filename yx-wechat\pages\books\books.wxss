/* pages/books/books.wxss */
.container {
  padding: 24rpx;
}

/* 书籍列表 */
.books-list {
  background: #fff;
  border-radius: 16rpx;
  overflow: hidden;
}

.book-item {
  display: flex;
  align-items: center;
  padding: 32rpx;
  border-bottom: 1rpx solid #ebedf0;
  transition: background-color 0.2s;
}

.book-item:last-child {
  border-bottom: none;
}

.book-item:active {
  background-color: #f7f8fa;
}

.book-cover {
  width: 80rpx;
  height: 80rpx;
  background: #f7f8fa;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.book-info {
  flex: 1;
  margin-right: 16rpx;
}

.book-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #323233;
  margin-bottom: 12rpx;
}

.book-meta {
  display: flex;
  gap: 16rpx;
  margin-bottom: 12rpx;
}

.book-stats {
  display: flex;
  gap: 24rpx;
}

.stat-text {
  font-size: 24rpx;
  color: #969799;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #969799;
  margin-top: 24rpx;
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  background: #fff;
  border-radius: 16rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #969799;
  margin-top: 16rpx;
}
