<template>
  <div class="dashboard">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-content">
            <div class="card-icon user-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="card-info">
              <div class="card-title">用户总数</div>
              <div class="card-value">{{ statistics.userCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-content">
            <div class="card-icon role-icon">
              <i class="el-icon-s-custom"></i>
            </div>
            <div class="card-info">
              <div class="card-title">角色总数</div>
              <div class="card-value">{{ statistics.roleCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-content">
            <div class="card-icon menu-icon">
              <i class="el-icon-menu"></i>
            </div>
            <div class="card-info">
              <div class="card-title">菜单总数</div>
              <div class="card-value">{{ statistics.menuCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
      
      <el-col :span="6">
        <el-card class="box-card">
          <div class="card-content">
            <div class="card-icon log-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="card-info">
              <div class="card-title">今日日志</div>
              <div class="card-value">{{ statistics.logCount }}</div>
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card>
          <div slot="header">
            <span>系统信息</span>
          </div>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="系统名称">YX刷题小程序管理系统</el-descriptions-item>
            <el-descriptions-item label="系统版本">v1.0.0</el-descriptions-item>
            <el-descriptions-item label="技术栈">Vue2 + Element UI</el-descriptions-item>
            <el-descriptions-item label="服务器时间">{{ currentTime }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card>
          <div slot="header">
            <span>快捷操作</span>
          </div>
          <div class="quick-actions">
            <el-button type="primary" icon="el-icon-plus" @click="$router.push('/system/user')">
              添加用户
            </el-button>
            <el-button type="success" icon="el-icon-plus" @click="$router.push('/system/role')">
              添加角色
            </el-button>
            <el-button type="info" icon="el-icon-plus" @click="$router.push('/system/menu')">
              添加菜单
            </el-button>
            <el-button type="warning" icon="el-icon-view" @click="$router.push('/system/log')">
              查看日志
            </el-button>
          </div>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
export default {
  name: 'Dashboard',
  data() {
    return {
      statistics: {
        userCount: 156,
        roleCount: 8,
        menuCount: 24,
        logCount: 89
      },
      currentTime: ''
    }
  },
  mounted() {
    this.updateTime()
    this.timer = setInterval(this.updateTime, 1000)
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer)
    }
  },
  methods: {
    updateTime() {
      this.currentTime = new Date().toLocaleString()
    }
  }
}
</script>

<style lang="scss" scoped>
.dashboard {
  .box-card {
    .card-content {
      display: flex;
      align-items: center;
      
      .card-icon {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 20px;
        
        i {
          font-size: 24px;
          color: #fff;
        }
        
        &.user-icon {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        &.role-icon {
          background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        &.menu-icon {
          background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        &.log-icon {
          background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
      }
      
      .card-info {
        .card-title {
          font-size: 14px;
          color: #999;
          margin-bottom: 8px;
        }
        
        .card-value {
          font-size: 28px;
          font-weight: bold;
          color: #333;
        }
      }
    }
  }
  
  .quick-actions {
    .el-button {
      margin-right: 10px;
      margin-bottom: 10px;
    }
  }
}
</style>
