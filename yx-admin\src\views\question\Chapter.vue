<template>
  <div class="chapter-management">
    <el-card>
      <!-- 面包屑导航 -->
      <div class="breadcrumb-area" v-if="currentBook">
        <el-breadcrumb separator="/">
          <el-breadcrumb-item :to="{ path: '/question/bank' }">题库管理</el-breadcrumb-item>
          <el-breadcrumb-item :to="{ path: '/question/book', query: { bankId: bankId, bankName: currentBank } }">
            {{ currentBank }}
          </el-breadcrumb-item>
          <el-breadcrumb-item>{{ currentBook }}</el-breadcrumb-item>
        </el-breadcrumb>
      </div>

      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="章节名称">
            <el-input v-model="searchForm.name" placeholder="请输入章节名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="类型">
            <el-select v-model="searchForm.type" placeholder="请选择类型" clearable>
              <el-option label="章" value="chapter"></el-option>
              <el-option label="节" value="section"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="启用" value="1"></el-option>
              <el-option label="禁用" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleAdd">新增章节</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table
        :data="tableData"
        style="width: 100%"
        v-loading="loading"
        row-key="id"
        :tree-props="{children: 'children', hasChildren: 'hasChildren'}"
        default-expand-all>
        <el-table-column prop="name" label="章节名称" width="300"></el-table-column>
        <el-table-column prop="type" label="类型" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.type === 'chapter' ? 'primary' : 'success'">
              {{ scope.row.type === 'chapter' ? '章' : '节' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="questionCount" label="题目数量" width="100">
          <template slot-scope="scope">
            <el-tag type="info">{{ scope.row.questionCount }}题</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="sort" label="排序" width="80"></el-table-column>
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="160"></el-table-column>
        <el-table-column label="操作" width="300">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)">编辑</el-button>
            <el-button size="mini" type="success" @click="handleAddChild(scope.row)" v-if="scope.row.type === 'chapter'">
              新增节
            </el-button>
            <el-button size="mini" type="primary" @click="handleQuestions(scope.row)">题目</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="上级章节" prop="parentId" v-if="!isAddChild">
          <el-select v-model="form.parentId" placeholder="请选择上级章节" clearable style="width: 100%;">
            <el-option label="顶级章节" :value="0"></el-option>
            <el-option
              v-for="chapter in chapterOptions"
              :key="chapter.id"
              :label="chapter.name"
              :value="chapter.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="章节类型" prop="type" v-if="!isAddChild">
          <el-radio-group v-model="form.type">
            <el-radio label="chapter">章</el-radio>
            <el-radio label="section">节</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="章节名称" prop="name">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" rows="3"></el-input>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number v-model="form.sort" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'Chapter',
  data() {
    return {
      loading: false,
      currentBank: '',
      currentBook: '',
      bankId: null,
      bookId: null,
      isAddChild: false,
      searchForm: {
        name: '',
        type: '',
        status: ''
      },
      tableData: [
        {
          id: 1,
          name: '第一章 认识数字',
          type: 'chapter',
          questionCount: 45,
          sort: 1,
          status: 1,
          parentId: 0,
          createTime: '2024-01-01 10:00:00',
          children: [
            {
              id: 11,
              name: '第一节 数字1-5',
              type: 'section',
              questionCount: 15,
              sort: 1,
              status: 1,
              parentId: 1,
              createTime: '2024-01-01 10:00:00'
            },
            {
              id: 12,
              name: '第二节 数字6-10',
              type: 'section',
              questionCount: 18,
              sort: 2,
              status: 1,
              parentId: 1,
              createTime: '2024-01-01 10:00:00'
            },
            {
              id: 13,
              name: '第三节 数字比较',
              type: 'section',
              questionCount: 12,
              sort: 3,
              status: 1,
              parentId: 1,
              createTime: '2024-01-01 10:00:00'
            }
          ]
        },
        {
          id: 2,
          name: '第二章 简单加法',
          type: 'chapter',
          questionCount: 38,
          sort: 2,
          status: 1,
          parentId: 0,
          createTime: '2024-01-01 10:00:00',
          children: [
            {
              id: 21,
              name: '第一节 10以内加法',
              type: 'section',
              questionCount: 20,
              sort: 1,
              status: 1,
              parentId: 2,
              createTime: '2024-01-01 10:00:00'
            },
            {
              id: 22,
              name: '第二节 加法应用题',
              type: 'section',
              questionCount: 18,
              sort: 2,
              status: 1,
              parentId: 2,
              createTime: '2024-01-01 10:00:00'
            }
          ]
        }
      ],
      dialogVisible: false,
      isEdit: false,
      form: {
        id: null,
        parentId: 0,
        name: '',
        type: 'chapter',
        description: '',
        sort: 0,
        status: 1
      },
      rules: {
        name: [
          { required: true, message: '请输入章节名称', trigger: 'blur' }
        ],
        type: [
          { required: true, message: '请选择章节类型', trigger: 'change' }
        ]
      },
      chapterOptions: []
    }
  },
  computed: {
    dialogTitle() {
      if (this.isAddChild) {
        return '新增节'
      }
      return this.isEdit ? '编辑章节' : '新增章节'
    }
  },
  methods: {
    handleSearch() {
      this.loadData()
    },
    handleReset() {
      this.searchForm = {
        name: '',
        type: '',
        status: ''
      }
      this.loadData()
    },
    handleAdd() {
      this.isEdit = false
      this.isAddChild = false
      this.form = {
        id: null,
        parentId: 0,
        name: '',
        type: 'chapter',
        description: '',
        sort: 0,
        status: 1
      }
      this.dialogVisible = true
    },
    handleAddChild(row) {
      this.isEdit = false
      this.isAddChild = true
      this.form = {
        id: null,
        parentId: row.id,
        name: '',
        type: 'section',
        description: '',
        sort: 0,
        status: 1
      }
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.isEdit = true
      this.isAddChild = false
      this.form = { ...row }
      this.dialogVisible = true
    },
    handleQuestions(row) {
      this.$router.push({
        path: '/question/list',
        query: { 
          bankId: this.bankId,
          bankName: this.currentBank,
          bookId: this.bookId,
          bookName: this.currentBook,
          chapterId: row.id, 
          chapterName: row.name 
        }
      })
    },
    handleDelete(row) {
      this.$confirm('确定要删除该章节吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.$message.success('删除成功')
        this.loadData()
      })
    },
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.$message.success(this.isEdit ? '编辑成功' : '新增成功')
          this.dialogVisible = false
          this.loadData()
        }
      })
    },
    loadData() {
      this.loading = true
      // 模拟API调用
      setTimeout(() => {
        this.loading = false
      }, 500)
      
      // 构建章节选项
      this.buildChapterOptions(this.tableData)
    },
    buildChapterOptions(data) {
      this.chapterOptions = []
      const traverse = (nodes, level = 0) => {
        nodes.forEach(node => {
          if (node.type === 'chapter') {
            this.chapterOptions.push({
              id: node.id,
              name: '　'.repeat(level) + node.name
            })
            if (node.children) {
              traverse(node.children, level + 1)
            }
          }
        })
      }
      traverse(data, 0)
    }
  },
  mounted() {
    // 获取路由参数
    this.bankId = this.$route.query.bankId
    this.currentBank = this.$route.query.bankName
    this.bookId = this.$route.query.bookId
    this.currentBook = this.$route.query.bookName
    this.loadData()
  }
}
</script>

<style lang="scss" scoped>
.chapter-management {
  .breadcrumb-area {
    margin-bottom: 20px;
  }
  
  .search-area {
    margin-bottom: 20px;
    
    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
}
</style>
