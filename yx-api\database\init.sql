-- YX刷题小程序数据库初始化脚本

-- 用户表
CREATE TABLE IF NOT EXISTS `users` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `email` varchar(100) NOT NULL COMMENT '邮箱',
  `password` varchar(255) NOT NULL COMMENT '密码',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `last_login_time` datetime DEFAULT NULL COMMENT '最后登录时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username` (`username`),
  UNIQUE KEY `email` (`email`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 角色表
CREATE TABLE IF NOT EXISTS `roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '角色名称',
  `code` varchar(50) NOT NULL COMMENT '角色编码',
  `description` text COMMENT '角色描述',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色表';

-- 权限表
CREATE TABLE IF NOT EXISTS `permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '权限名称',
  `code` varchar(50) NOT NULL COMMENT '权限编码',
  `type` varchar(20) DEFAULT 'menu' COMMENT '权限类型：menu-菜单，button-按钮，api-接口',
  `parent_id` int(11) DEFAULT 0 COMMENT '父权限ID',
  `path` varchar(255) DEFAULT NULL COMMENT '路径',
  `icon` varchar(50) DEFAULT NULL COMMENT '图标',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='权限表';

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS `user_roles` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `role_id` int(11) NOT NULL COMMENT '角色ID',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `user_role` (`user_id`, `role_id`),
  KEY `user_id` (`user_id`),
  KEY `role_id` (`role_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户角色关联表';

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS `role_permissions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `role_id` int(11) NOT NULL COMMENT '角色ID',
  `permission_id` int(11) NOT NULL COMMENT '权限ID',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `role_permission` (`role_id`, `permission_id`),
  KEY `role_id` (`role_id`),
  KEY `permission_id` (`permission_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='角色权限关联表';

-- 菜单表
CREATE TABLE IF NOT EXISTS `menus` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '菜单名称',
  `path` varchar(255) DEFAULT NULL COMMENT '菜单路径',
  `component` varchar(255) DEFAULT NULL COMMENT '组件路径',
  `icon` varchar(50) DEFAULT NULL COMMENT '菜单图标',
  `parent_id` int(11) DEFAULT 0 COMMENT '父菜单ID',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `type` varchar(20) DEFAULT 'menu' COMMENT '类型：menu-菜单，button-按钮',
  `permission` varchar(100) DEFAULT NULL COMMENT '权限标识',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-显示，0-隐藏',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='菜单表';

-- 系统日志表
CREATE TABLE IF NOT EXISTS `system_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) DEFAULT NULL COMMENT '操作用户ID',
  `username` varchar(50) DEFAULT NULL COMMENT '操作用户名',
  `operation` varchar(100) NOT NULL COMMENT '操作类型',
  `method` varchar(10) NOT NULL COMMENT '请求方法',
  `url` varchar(255) NOT NULL COMMENT '请求URL',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `request_data` text COMMENT '请求数据',
  `response_data` text COMMENT '响应数据',
  `status` int(11) DEFAULT NULL COMMENT '响应状态码',
  `duration` int(11) DEFAULT NULL COMMENT '执行时间(ms)',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `created_time` (`created_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='系统日志表';

-- 登录日志表
CREATE TABLE IF NOT EXISTS `login_logs` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `ip_address` varchar(45) DEFAULT NULL COMMENT 'IP地址',
  `user_agent` text COMMENT '用户代理',
  `login_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '登录时间',
  `status` tinyint(1) DEFAULT 1 COMMENT '登录状态：1-成功，0-失败',
  PRIMARY KEY (`id`),
  KEY `user_id` (`user_id`),
  KEY `login_time` (`login_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='登录日志表';

-- 学科表
CREATE TABLE IF NOT EXISTS `subjects` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '学科名称',
  `code` varchar(50) NOT NULL COMMENT '学科编码',
  `icon` varchar(255) DEFAULT NULL COMMENT '学科图标',
  `color` varchar(20) DEFAULT NULL COMMENT '主题色',
  `description` text COMMENT '学科描述',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='学科表';

-- 题库表
CREATE TABLE IF NOT EXISTS `question_banks` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '题库名称',
  `subject_id` int(11) NOT NULL COMMENT '学科ID',
  `description` text COMMENT '题库描述',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `subject_id` (`subject_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题库表';

-- 书籍表
CREATE TABLE IF NOT EXISTS `books` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '书籍名称',
  `question_bank_id` int(11) NOT NULL COMMENT '题库ID',
  `grade` varchar(20) DEFAULT NULL COMMENT '年级',
  `version` varchar(50) DEFAULT NULL COMMENT '版本',
  `description` text COMMENT '书籍描述',
  `cover` varchar(255) DEFAULT NULL COMMENT '封面图片',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `question_bank_id` (`question_bank_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='书籍表';

-- 章节表
CREATE TABLE IF NOT EXISTS `chapters` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(100) NOT NULL COMMENT '章节名称',
  `book_id` int(11) NOT NULL COMMENT '书籍ID',
  `parent_id` int(11) DEFAULT 0 COMMENT '父章节ID，0表示章',
  `type` varchar(20) DEFAULT 'chapter' COMMENT '类型：chapter-章，section-节',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `description` text COMMENT '章节描述',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `book_id` (`book_id`),
  KEY `parent_id` (`parent_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='章节表';

-- 题目分类表
CREATE TABLE IF NOT EXISTS `question_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `name` varchar(50) NOT NULL COMMENT '分类名称',
  `code` varchar(50) NOT NULL COMMENT '分类编码',
  `type` varchar(20) NOT NULL COMMENT '题目类型：single-单选，multiple-多选，judge-判断，fill-填空，essay-简答',
  `difficulty` tinyint(1) DEFAULT 1 COMMENT '难度等级：1-简单，2-中等，3-困难',
  `description` text COMMENT '分类描述',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `code` (`code`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题目分类表';

-- 题目表
CREATE TABLE IF NOT EXISTS `questions` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `title` text NOT NULL COMMENT '题目标题',
  `content` text COMMENT '题目内容',
  `chapter_id` int(11) NOT NULL COMMENT '章节ID',
  `category_id` int(11) NOT NULL COMMENT '分类ID',
  `type` varchar(20) NOT NULL COMMENT '题目类型：single-单选，multiple-多选，judge-判断，essay-简答',
  `difficulty` tinyint(1) DEFAULT 1 COMMENT '难度等级：1-简单，2-中等，3-困难',
  `score` decimal(5,2) DEFAULT 1.00 COMMENT '分值',
  `options` json DEFAULT NULL COMMENT '选项数据（JSON格式）',
  `answer` text COMMENT '正确答案',
  `analysis` text COMMENT '答案解析',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-启用，0-禁用',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `chapter_id` (`chapter_id`),
  KEY `category_id` (`category_id`),
  KEY `type` (`type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题目表';

-- 题目选项表（用于单选题和多选题）
CREATE TABLE IF NOT EXISTS `question_options` (
  `id` int(11) NOT NULL AUTO_INCREMENT,
  `question_id` int(11) NOT NULL COMMENT '题目ID',
  `option_key` varchar(10) NOT NULL COMMENT '选项标识（A、B、C、D等）',
  `option_text` text NOT NULL COMMENT '选项内容',
  `is_correct` tinyint(1) DEFAULT 0 COMMENT '是否正确答案：1-是，0-否',
  `sort` int(11) DEFAULT 0 COMMENT '排序',
  `created_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `question_id` (`question_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='题目选项表';

-- 插入初始数据

-- 插入默认管理员用户 (密码: 123456)
INSERT INTO `users` (`username`, `email`, `password`, `status`, `created_time`) VALUES
('admin', '<EMAIL>', '$2a$10$wM5dgQwmTlo00bHrrdRyQeuYgdxuNemow9Egird2UV2/VoDbe609.', 1, NOW());

-- 插入默认角色
INSERT INTO `roles` (`name`, `code`, `description`, `status`, `created_time`) VALUES
('超级管理员', 'super_admin', '系统超级管理员，拥有所有权限', 1, NOW()),
('管理员', 'admin', '系统管理员', 1, NOW()),
('教师', 'teacher', '教师角色，可以管理题库和题目', 1, NOW()),
('学生', 'student', '学生角色，只能查看和答题', 1, NOW());

-- 插入默认权限
INSERT INTO `permissions` (`name`, `code`, `type`, `parent_id`, `path`, `icon`, `sort`, `status`, `created_time`) VALUES
-- 系统管理
('系统管理', 'system', 'menu', 0, '/system', 'el-icon-setting', 1, 1, NOW()),
('用户管理', 'system:user', 'menu', 1, '/system/user', 'el-icon-user', 1, 1, NOW()),
('角色管理', 'system:role', 'menu', 1, '/system/role', 'el-icon-s-custom', 2, 1, NOW()),
('菜单管理', 'system:menu', 'menu', 1, '/system/menu', 'el-icon-menu', 3, 1, NOW()),
('权限管理', 'system:permission', 'menu', 1, '/system/permission', 'el-icon-key', 4, 1, NOW()),
('系统日志', 'system:log', 'menu', 1, '/system/log', 'el-icon-document', 5, 1, NOW()),
('学科管理', 'system:subject', 'menu', 1, '/system/subject', 'el-icon-collection', 6, 1, NOW()),

-- 题库管理
('题库管理', 'question', 'menu', 0, '/question', 'el-icon-notebook-1', 2, 1, NOW()),
('题库列表', 'question:bank', 'menu', 8, '/question/bank', 'el-icon-folder', 1, 1, NOW()),
('书籍管理', 'question:book', 'menu', 8, '/question/book', 'el-icon-reading', 2, 1, NOW()),
('章节管理', 'question:chapter', 'menu', 8, '/question/chapter', 'el-icon-document-copy', 3, 1, NOW()),
('题目分类', 'question:category', 'menu', 8, '/question/category', 'el-icon-files', 4, 1, NOW()),
('题目列表', 'question:list', 'menu', 8, '/question/list', 'el-icon-edit-outline', 5, 1, NOW()),

-- 用户管理按钮权限
('新增用户', 'system:user:add', 'button', 2, '', '', 1, 1, NOW()),
('编辑用户', 'system:user:edit', 'button', 2, '', '', 2, 1, NOW()),
('删除用户', 'system:user:delete', 'button', 2, '', '', 3, 1, NOW()),
('重置密码', 'system:user:reset', 'button', 2, '', '', 4, 1, NOW()),

-- 角色管理按钮权限
('新增角色', 'system:role:add', 'button', 3, '', '', 1, 1, NOW()),
('编辑角色', 'system:role:edit', 'button', 3, '', '', 2, 1, NOW()),
('删除角色', 'system:role:delete', 'button', 3, '', '', 3, 1, NOW()),
('分配权限', 'system:role:permission', 'button', 3, '', '', 4, 1, NOW()),

-- 菜单管理按钮权限
('新增菜单', 'system:menu:add', 'button', 4, '', '', 1, 1, NOW()),
('编辑菜单', 'system:menu:edit', 'button', 4, '', '', 2, 1, NOW()),
('删除菜单', 'system:menu:delete', 'button', 4, '', '', 3, 1, NOW()),

-- 权限管理按钮权限
('新增权限', 'system:permission:add', 'button', 5, '', '', 1, 1, NOW()),
('编辑权限', 'system:permission:edit', 'button', 5, '', '', 2, 1, NOW()),
('删除权限', 'system:permission:delete', 'button', 5, '', '', 3, 1, NOW()),

-- 学科管理按钮权限
('新增学科', 'system:subject:add', 'button', 7, '', '', 1, 1, NOW()),
('编辑学科', 'system:subject:edit', 'button', 7, '', '', 2, 1, NOW()),
('删除学科', 'system:subject:delete', 'button', 7, '', '', 3, 1, NOW()),

-- 题库管理按钮权限
('新增题库', 'question:bank:add', 'button', 9, '', '', 1, 1, NOW()),
('编辑题库', 'question:bank:edit', 'button', 9, '', '', 2, 1, NOW()),
('删除题库', 'question:bank:delete', 'button', 9, '', '', 3, 1, NOW()),

-- 书籍管理按钮权限
('新增书籍', 'question:book:add', 'button', 10, '', '', 1, 1, NOW()),
('编辑书籍', 'question:book:edit', 'button', 10, '', '', 2, 1, NOW()),
('删除书籍', 'question:book:delete', 'button', 10, '', '', 3, 1, NOW()),

-- 章节管理按钮权限
('新增章节', 'question:chapter:add', 'button', 11, '', '', 1, 1, NOW()),
('编辑章节', 'question:chapter:edit', 'button', 11, '', '', 2, 1, NOW()),
('删除章节', 'question:chapter:delete', 'button', 11, '', '', 3, 1, NOW()),

-- 题目分类按钮权限
('新增分类', 'question:category:add', 'button', 12, '', '', 1, 1, NOW()),
('编辑分类', 'question:category:edit', 'button', 12, '', '', 2, 1, NOW()),
('删除分类', 'question:category:delete', 'button', 12, '', '', 3, 1, NOW()),

-- 题目管理按钮权限
('新增题目', 'question:list:add', 'button', 13, '', '', 1, 1, NOW()),
('编辑题目', 'question:list:edit', 'button', 13, '', '', 2, 1, NOW()),
('删除题目', 'question:list:delete', 'button', 13, '', '', 3, 1, NOW()),
('导入题目', 'question:list:import', 'button', 13, '', '', 4, 1, NOW()),
('导出题目', 'question:list:export', 'button', 13, '', '', 5, 1, NOW());

-- 插入菜单数据
INSERT INTO `menus` (`name`, `path`, `component`, `icon`, `parent_id`, `sort`, `type`, `permission`, `status`, `created_time`) VALUES
-- 首页
('首页', '/dashboard', 'Dashboard', 'el-icon-s-home', 0, 0, 'menu', '', 1, NOW()),

-- 系统管理
('系统管理', '/system', 'Layout', 'el-icon-setting', 0, 1, 'menu', 'system', 1, NOW()),
('用户管理', '/system/user', 'system/User', 'el-icon-user', 2, 1, 'menu', 'system:user', 1, NOW()),
('角色管理', '/system/role', 'system/Role', 'el-icon-s-custom', 2, 2, 'menu', 'system:role', 1, NOW()),
('菜单管理', '/system/menu', 'system/Menu', 'el-icon-menu', 2, 3, 'menu', 'system:menu', 1, NOW()),
('权限管理', '/system/permission', 'system/Permission', 'el-icon-key', 2, 4, 'menu', 'system:permission', 1, NOW()),
('系统日志', '/system/log', 'system/Log', 'el-icon-document', 2, 5, 'menu', 'system:log', 1, NOW()),
('学科管理', '/system/subject', 'system/Subject', 'el-icon-collection', 2, 6, 'menu', 'system:subject', 1, NOW()),

-- 题库管理
('题库管理', '/question', 'Layout', 'el-icon-notebook-1', 0, 2, 'menu', 'question', 1, NOW()),
('题库列表', '/question/bank', 'question/Bank', 'el-icon-folder', 9, 1, 'menu', 'question:bank', 1, NOW()),
('书籍管理', '/question/book', 'question/Book', 'el-icon-reading', 9, 2, 'menu', 'question:book', 1, NOW()),
('章节管理', '/question/chapter', 'question/Chapter', 'el-icon-document-copy', 9, 3, 'menu', 'question:chapter', 1, NOW()),
('题目分类', '/question/category', 'question/Category', 'el-icon-files', 9, 4, 'menu', 'question:category', 1, NOW()),
('题目列表', '/question/list', 'question/List', 'el-icon-edit-outline', 9, 5, 'menu', 'question:list', 1, NOW());

-- 分配超级管理员角色给admin用户
INSERT INTO `user_roles` (`user_id`, `role_id`, `created_time`) VALUES
(1, 1, NOW());

-- 分配所有权限给超级管理员角色
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `created_time`)
SELECT 1, id, NOW() FROM `permissions` WHERE `status` = 1;

-- 插入测试学科数据
INSERT INTO `subjects` (`name`, `code`, `icon`, `color`, `description`, `sort`, `status`, `created_time`) VALUES
('数学', 'math', 'el-icon-s-data', '#409EFF', '数学学科', 1, 1, NOW()),
('语文', 'chinese', 'el-icon-reading', '#67C23A', '语文学科', 2, 1, NOW()),
('英语', 'english', 'el-icon-chat-dot-round', '#E6A23C', '英语学科', 3, 1, NOW()),
('物理', 'physics', 'el-icon-lightning', '#F56C6C', '物理学科', 4, 1, NOW()),
('化学', 'chemistry', 'el-icon-magic-stick', '#909399', '化学学科', 5, 1, NOW());

-- 插入测试题库数据
INSERT INTO `question_banks` (`name`, `subject_id`, `description`, `status`, `created_time`) VALUES
('小学数学题库', 1, '小学阶段数学题目集合', 1, NOW()),
('初中数学题库', 1, '初中阶段数学题目集合', 1, NOW()),
('高中数学题库', 1, '高中阶段数学题目集合', 1, NOW()),
('小学语文题库', 2, '小学阶段语文题目集合', 1, NOW()),
('初中语文题库', 2, '初中阶段语文题目集合', 1, NOW());

-- 插入测试书籍数据
INSERT INTO `books` (`name`, `question_bank_id`, `grade`, `version`, `description`, `status`, `created_time`) VALUES
('小学数学一年级上册', 1, '一年级', '人教版', '小学一年级上册数学教材', 1, NOW()),
('小学数学一年级下册', 1, '一年级', '人教版', '小学一年级下册数学教材', 1, NOW()),
('小学数学二年级上册', 1, '二年级', '人教版', '小学二年级上册数学教材', 1, NOW()),
('初中数学七年级上册', 2, '七年级', '人教版', '初中七年级上册数学教材', 1, NOW()),
('初中数学七年级下册', 2, '七年级', '人教版', '初中七年级下册数学教材', 1, NOW());

-- 插入测试章节数据
INSERT INTO `chapters` (`name`, `book_id`, `parent_id`, `type`, `sort`, `description`, `status`, `created_time`) VALUES
-- 小学数学一年级上册章节
('第一章 数一数', 1, 0, 'chapter', 1, '学习数数的基本概念', 1, NOW()),
('1.1 数数', 1, 1, 'section', 1, '学习1-10的数数', 1, NOW()),
('1.2 比多少', 1, 1, 'section', 2, '学习比较多少', 1, NOW()),
('第二章 比一比', 1, 0, 'chapter', 2, '学习比较的概念', 1, NOW()),
('2.1 比长短', 1, 4, 'section', 1, '学习比较长短', 1, NOW()),
('2.2 比高矮', 1, 4, 'section', 2, '学习比较高矮', 1, NOW()),

-- 初中数学七年级上册章节
('第一章 有理数', 4, 0, 'chapter', 1, '有理数的基本概念和运算', 1, NOW()),
('1.1 正数和负数', 4, 7, 'section', 1, '正数和负数的概念', 1, NOW()),
('1.2 有理数', 4, 7, 'section', 2, '有理数的定义和分类', 1, NOW()),
('1.3 有理数的加减法', 4, 7, 'section', 3, '有理数加减法运算', 1, NOW());

-- 插入测试题目分类数据
INSERT INTO `question_categories` (`name`, `code`, `type`, `difficulty`, `description`, `status`, `created_time`) VALUES
('单选题-简单', 'single_easy', 'single', 1, '简单的单选题', 1, NOW()),
('单选题-中等', 'single_medium', 'single', 2, '中等难度的单选题', 1, NOW()),
('单选题-困难', 'single_hard', 'single', 3, '困难的单选题', 1, NOW()),
('多选题-简单', 'multiple_easy', 'multiple', 1, '简单的多选题', 1, NOW()),
('多选题-中等', 'multiple_medium', 'multiple', 2, '中等难度的多选题', 1, NOW()),
('判断题-简单', 'judge_easy', 'judge', 1, '简单的判断题', 1, NOW()),
('填空题-简单', 'fill_easy', 'fill', 1, '简单的填空题', 1, NOW()),
('简答题-中等', 'essay_medium', 'essay', 2, '中等难度的简答题', 1, NOW());

-- 插入测试题目数据
INSERT INTO `questions` (`title`, `content`, `chapter_id`, `category_id`, `type`, `difficulty`, `score`, `answer`, `analysis`, `status`, `created_time`) VALUES
-- 小学数学一年级题目
('数数练习', '请数一数图中有几个苹果？', 2, 1, 'single', 1, 1.00, 'A', '仔细观察图片，逐个数出苹果的数量', 1, NOW()),
('比较大小', '3和5哪个大？', 2, 1, 'single', 1, 1.00, 'B', '5比3大，所以选择5', 1, NOW()),
('长短比较', '铅笔和尺子哪个长？', 5, 1, 'single', 1, 1.00, 'B', '通过观察可以看出尺子比铅笔长', 1, NOW()),
('高矮判断', '大树比小草高吗？', 6, 6, 'judge', 1, 1.00, '对', '大树明显比小草高，所以判断为对', 1, NOW()),

-- 初中数学七年级题目
('正负数概念', '下列哪个是负数？', 8, 1, 'single', 1, 2.00, 'C', '负数是小于0的数，-5是负数', 1, NOW()),
('有理数分类', '有理数包括哪些数？', 9, 4, 'multiple', 2, 3.00, 'A,B,C', '有理数包括正整数、负整数、正分数、负分数和零', 1, NOW()),
('有理数加法', '计算：(-3) + 5 = ?', 10, 7, 'fill', 2, 2.00, '2', '负数加正数，用正数减去负数的绝对值：5-3=2', 1, NOW()),
('有理数运算规律', '请说明有理数加法的交换律', 10, 8, 'essay', 2, 5.00, '对于任意两个有理数a和b，都有a+b=b+a', '有理数加法满足交换律，即改变加数的位置，和不变', 1, NOW());

-- 插入题目选项数据
INSERT INTO `question_options` (`question_id`, `option_key`, `option_text`, `is_correct`, `sort`, `created_time`) VALUES
-- 题目1的选项
(1, 'A', '3个', 1, 1, NOW()),
(1, 'B', '4个', 0, 2, NOW()),
(1, 'C', '5个', 0, 3, NOW()),
(1, 'D', '6个', 0, 4, NOW()),

-- 题目2的选项
(2, 'A', '3', 0, 1, NOW()),
(2, 'B', '5', 1, 2, NOW()),
(2, 'C', '一样大', 0, 3, NOW()),
(2, 'D', '无法比较', 0, 4, NOW()),

-- 题目3的选项
(3, 'A', '铅笔', 0, 1, NOW()),
(3, 'B', '尺子', 1, 2, NOW()),
(3, 'C', '一样长', 0, 3, NOW()),
(3, 'D', '无法比较', 0, 4, NOW()),

-- 题目5的选项
(5, 'A', '3', 0, 1, NOW()),
(5, 'B', '0', 0, 2, NOW()),
(5, 'C', '-5', 1, 3, NOW()),
(5, 'D', '8', 0, 4, NOW()),

-- 题目6的选项
(6, 'A', '正整数', 1, 1, NOW()),
(6, 'B', '负整数', 1, 2, NOW()),
(6, 'C', '分数', 1, 3, NOW()),
(6, 'D', '无理数', 0, 4, NOW());

-- 插入更多测试用户
INSERT INTO `users` (`username`, `email`, `password`, `status`, `created_time`) VALUES
('teacher1', '<EMAIL>', '$2a$10$wM5dgQwmTlo00bHrrdRyQeuYgdxuNemow9Egird2UV2/VoDbe609.', 1, NOW()),
('teacher2', '<EMAIL>', '$2a$10$wM5dgQwmTlo00bHrrdRyQeuYgdxuNemow9Egird2UV2/VoDbe609.', 1, NOW()),
('student1', '<EMAIL>', '$2a$10$wM5dgQwmTlo00bHrrdRyQeuYgdxuNemow9Egird2UV2/VoDbe609.', 1, NOW()),
('student2', '<EMAIL>', '$2a$10$wM5dgQwmTlo00bHrrdRyQeuYgdxuNemow9Egird2UV2/VoDbe609.', 1, NOW());

-- 分配角色给测试用户
INSERT INTO `user_roles` (`user_id`, `role_id`, `created_time`) VALUES
(2, 3, NOW()), -- teacher1 -> teacher
(3, 3, NOW()), -- teacher2 -> teacher
(4, 4, NOW()), -- student1 -> student
(5, 4, NOW()); -- student2 -> student

-- 为教师角色分配权限（题库管理相关权限）
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `created_time`)
SELECT 3, id, NOW() FROM `permissions` WHERE `code` LIKE 'question%' AND `status` = 1;

-- 为学生角色分配基本查看权限
INSERT INTO `role_permissions` (`role_id`, `permission_id`, `created_time`)
SELECT 4, id, NOW() FROM `permissions` WHERE `code` IN ('question:bank', 'question:book', 'question:chapter', 'question:list') AND `status` = 1;
