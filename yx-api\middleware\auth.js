const jwt = require('jsonwebtoken');
const { response } = require('../utils/response');
const { query } = require('../config/database');

// JWT认证中间件
const authMiddleware = async (ctx, next) => {
  try {
    const token = ctx.headers.authorization?.replace('Bearer ', '');
    
    if (!token) {
      ctx.status = 401;
      ctx.body = response.error('未提供访问令牌', 401);
      return;
    }
    
    // 验证token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // 查询用户信息
    const users = await query(
      'SELECT id, username, email, status FROM users WHERE id = ?',
      [decoded.userId]
    );
    
    if (users.length === 0) {
      ctx.status = 401;
      ctx.body = response.error('用户不存在', 401);
      return;
    }
    
    const user = users[0];
    
    if (user.status !== 1) {
      ctx.status = 401;
      ctx.body = response.error('用户已被禁用', 401);
      return;
    }
    
    // 将用户信息添加到上下文
    ctx.state.user = user;
    
    await next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      ctx.status = 401;
      ctx.body = response.error('无效的访问令牌', 401);
    } else if (error.name === 'TokenExpiredError') {
      ctx.status = 401;
      ctx.body = response.error('访问令牌已过期', 401);
    } else {
      throw error;
    }
  }
};

// 权限验证中间件
const permissionMiddleware = (permission) => {
  return async (ctx, next) => {
    try {
      const userId = ctx.state.user.id;
      
      // 查询用户权限
      const permissions = await query(`
        SELECT DISTINCT p.code 
        FROM permissions p
        JOIN role_permissions rp ON p.id = rp.permission_id
        JOIN user_roles ur ON rp.role_id = ur.role_id
        WHERE ur.user_id = ? AND p.status = 1
      `, [userId]);
      
      const userPermissions = permissions.map(p => p.code);
      
      if (!userPermissions.includes(permission)) {
        ctx.status = 403;
        ctx.body = response.error('权限不足', 403);
        return;
      }
      
      await next();
    } catch (error) {
      throw error;
    }
  };
};

module.exports = {
  authMiddleware,
  permissionMiddleware
};
