const Router = require('koa-router');
const { response } = require('../utils/response');
const { validation } = require('../utils/validation');
const { query } = require('../config/database');
const { authMiddleware, permissionMiddleware } = require('../middleware/auth');

const router = new Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取题目分类列表
router.get('/', permissionMiddleware('question:category'), async (ctx) => {
  const { page = 1, pageSize = 10, name, status } = ctx.query;

  let whereClause = 'WHERE 1=1';
  const params = [];

  if (name && name !== '') {
    whereClause += ' AND name LIKE ?';
    params.push(`%${name}%`);
  }

  if (status !== undefined && status !== '') {
    whereClause += ' AND status = ?';
    params.push(status);
  }

  // 查询总数
  const countSql = `SELECT COUNT(*) as total FROM question_categories ${whereClause}`;
  const countResult = await query(countSql, params);
  const total = countResult[0].total;

  // 查询数据
  const offset = (page - 1) * pageSize;
  const dataSql = `
    SELECT id, name, description, sort, status, created_time, updated_time
    FROM question_categories
    ${whereClause}
    ORDER BY sort ASC, created_time DESC
    LIMIT ${parseInt(pageSize)} OFFSET ${parseInt(offset)}
  `;

  const categories = await query(dataSql, params);

  ctx.body = response.page(categories, total, page, pageSize);
});

// 创建题目分类
router.post('/', permissionMiddleware('question:category'), async (ctx) => {
  const { name, description, sort } = ctx.request.body;

  // 参数验证
  validation.required(name, '分类名称');
  validation.length(name, 1, 50, '分类名称');

  // 创建分类
  const result = await query(
    'INSERT INTO question_categories (name, description, sort, status, created_time) VALUES (?, ?, ?, ?, ?)',
    [name, description || null, sort || 0, 1, new Date()]
  );

  ctx.body = response.success({ id: result.insertId, name }, '分类创建成功');
});

// 更新题目分类
router.put('/:id', permissionMiddleware('question:category'), async (ctx) => {
  const { id } = ctx.params;
  const { name, description, sort, status } = ctx.request.body;

  // 参数验证
  validation.required(name, '分类名称');
  validation.length(name, 1, 50, '分类名称');

  // 检查分类是否存在
  const existingCategories = await query('SELECT id FROM question_categories WHERE id = ?', [id]);

  if (existingCategories.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('分类不存在', 404);
    return;
  }

  // 更新分类
  await query(
    'UPDATE question_categories SET name = ?, description = ?, sort = ?, status = ?, updated_time = ? WHERE id = ?',
    [name, description || null, sort || 0, status, new Date(), id]
  );

  ctx.body = response.success(null, '分类更新成功');
});

// 删除题目分类
router.delete('/:id', permissionMiddleware('question:category'), async (ctx) => {
  const { id } = ctx.params;

  // 检查分类是否存在
  const existingCategories = await query('SELECT id FROM question_categories WHERE id = ?', [id]);

  if (existingCategories.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('分类不存在', 404);
    return;
  }

  // 检查是否有关联的题目
  const questions = await query('SELECT id FROM questions WHERE category_id = ?', [id]);

  if (questions.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('该分类下存在题目，无法删除', 400);
    return;
  }

  // 删除分类
  await query('DELETE FROM question_categories WHERE id = ?', [id]);

  ctx.body = response.success(null, '分类删除成功');
});

module.exports = router;
