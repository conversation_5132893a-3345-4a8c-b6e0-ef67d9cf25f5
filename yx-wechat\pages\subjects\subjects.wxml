<!--pages/subjects/subjects.wxml-->
<view class="container">
  <!-- 搜索栏 -->
  <view class="search-bar">
    <view class="search-input">
      <text class="search-icon">🔍</text>
      <input
        class="search-field"
        value="{{ searchKeyword }}"
        placeholder="搜索学科"
        bindinput="onSearchChange"
        bindconfirm="onSearch"
      />
    </view>
  </view>

  <!-- 学科列表 -->
  <view class="subjects-list" wx:if="{{ subjects.length > 0 }}">
    <view
      class="subject-item"
      wx:for="{{ filteredSubjects }}"
      wx:key="id"
      bind:tap="goToQuestionBanks"
      data-subject="{{ item }}"
    >
      <view class="subject-icon" style="background-color: {{ item.color || '#1989fa' }}">
        <text class="subject-emoji">📖</text>
      </view>
      <view class="subject-info">
        <view class="subject-name">{{ item.name }}</view>
        <view class="subject-desc">{{ item.description || '暂无描述' }}</view>
        <view class="subject-stats">
          <text class="stats-tag">{{ item.questionBankCount || 0 }}个题库</text>
        </view>
      </view>
      <text class="arrow-icon">→</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:elif="{{ !loading }}">
    <text class="empty-icon">🔍</text>
    <view class="empty-text">
      {{ searchKeyword ? '未找到相关学科' : '暂无学科数据' }}
    </view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{ loading }}">
    <text class="loading-icon">⏳</text>
    <view class="loading-text">加载中...</view>
  </view>
</view>
