const Router = require('koa-router');
const { response } = require('../utils/response');
const { query } = require('../config/database');
const { authMiddleware, permissionMiddleware } = require('../middleware/auth');

const router = new Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取权限列表
router.get('/', permissionMiddleware('system:permission'), async (ctx) => {
  const permissions = await query(
    'SELECT id, name, code, type, parent_id, path, icon, sort, status, created_time, updated_time FROM permissions ORDER BY sort ASC, created_time DESC'
  );

  ctx.body = response.success(permissions);
});

// 获取所有启用的权限（不分页）
router.get('/all', async (ctx) => {
  const permissions = await query(
    'SELECT id, name, code, type, parent_id, path, icon, sort FROM permissions WHERE status = 1 ORDER BY sort ASC, created_time DESC'
  );

  ctx.body = response.success(permissions);
});

// 创建权限
router.post('/', permissionMiddleware('system:permission'), async (ctx) => {
  const { name, code, type = 'menu', parentId = 0, path, icon, sort = 0, status = 1 } = ctx.request.body;

  // 参数验证
  if (!name || !code) {
    ctx.status = 400;
    ctx.body = response.error('权限名称和权限代码不能为空', 400);
    return;
  }

  // 检查权限代码是否已存在
  const existing = await query('SELECT id FROM permissions WHERE code = ?', [code]);
  if (existing.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('权限代码已存在', 400);
    return;
  }

  // 创建权限
  const result = await query(
    'INSERT INTO permissions (name, code, type, parent_id, path, icon, sort, status, created_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)',
    [name, code, type, parentId, path, icon, sort, status, new Date()]
  );

  ctx.body = response.success({ id: result.insertId }, '权限创建成功');
});

// 更新权限
router.put('/:id', permissionMiddleware('system:permission'), async (ctx) => {
  const { id } = ctx.params;
  const { name, code, type, parentId, path, icon, sort, status } = ctx.request.body;

  // 参数验证
  if (!name || !code) {
    ctx.status = 400;
    ctx.body = response.error('权限名称和权限代码不能为空', 400);
    return;
  }

  // 检查权限是否存在
  const existing = await query('SELECT id FROM permissions WHERE id = ?', [id]);
  if (existing.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('权限不存在', 404);
    return;
  }

  // 检查权限代码是否被其他权限使用
  const codeExists = await query('SELECT id FROM permissions WHERE code = ? AND id != ?', [code, id]);
  if (codeExists.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('权限代码已存在', 400);
    return;
  }

  // 更新权限
  await query(
    'UPDATE permissions SET name = ?, code = ?, type = ?, parent_id = ?, path = ?, icon = ?, sort = ?, status = ?, updated_time = ? WHERE id = ?',
    [name, code, type, parentId, path, icon, sort, status, new Date(), id]
  );

  ctx.body = response.success(null, '权限更新成功');
});

// 删除权限
router.delete('/:id', permissionMiddleware('system:permission'), async (ctx) => {
  const { id } = ctx.params;

  // 检查是否有子权限
  const children = await query('SELECT id FROM permissions WHERE parent_id = ?', [id]);
  if (children.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('存在子权限，无法删除', 400);
    return;
  }

  // 检查是否被角色使用
  const rolePermissions = await query('SELECT id FROM role_permissions WHERE permission_id = ?', [id]);
  if (rolePermissions.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('权限正在被角色使用，无法删除', 400);
    return;
  }

  // 删除权限
  await query('DELETE FROM permissions WHERE id = ?', [id]);

  ctx.body = response.success(null, '权限删除成功');
});

module.exports = router;
