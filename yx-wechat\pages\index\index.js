// pages/index/index.js
const auth = require('../../utils/auth.js')
const api = require('../../utils/api.js')
const storage = require('../../utils/storage.js')

Page({
  data: {
    userInfo: null,
    statistics: {
      total: 0,
      correct: 0,
      correctRate: 0
    },
    recentSubjects: [],
    recommendedSubjects: []
  },

  onLoad() {
    this.checkLoginAndLoadData()
  },

  onShow() {
    this.checkLoginAndLoadData()
  },

  // 检查登录状态并加载数据
  checkLoginAndLoadData() {
    if (auth.checkLogin()) {
      this.setData({
        userInfo: auth.getUserInfo()
      })
      this.loadStatistics()
      this.loadRecommendedSubjects()
    } else {
      this.setData({
        userInfo: null,
        statistics: { total: 0, correct: 0, correctRate: 0 },
        recentSubjects: [],
        recommendedSubjects: []
      })
    }
  },

  // 加载统计数据
  loadStatistics() {
    const statistics = storage.progress.getStatistics()
    this.setData({ statistics })
  },

  // 加载推荐学科
  loadRecommendedSubjects() {
    api.subjects.all()
      .then(res => {
        const subjects = res.data || []
        this.setData({
          recommendedSubjects: subjects.slice(0, 6) // 只显示前6个
        })
      })
      .catch(err => {
        console.error('加载学科失败:', err)
      })
  },

  // 跳转到登录页
  goToLogin() {
    wx.navigateTo({
      url: '/pages/login/login'
    })
  },

  // 跳转到学科页面
  goToSubjects() {
    if (!auth.requireLogin()) return

    wx.switchTab({
      url: '/pages/subjects/subjects'
    })
  },

  // 跳转到统计页面
  goToStatistics() {
    if (!auth.requireLogin()) return

    wx.switchTab({
      url: '/pages/statistics/statistics'
    })
  },

  // 跳转到题库页面
  goToQuestionBanks(e) {
    if (!auth.requireLogin()) return

    const subject = e.currentTarget.dataset.subject
    wx.navigateTo({
      url: `/pages/question-banks/question-banks?subjectId=${subject.id}&subjectName=${subject.name}`
    })
  }
})
