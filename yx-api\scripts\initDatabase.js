const fs = require('fs');
const path = require('path');
const mysql = require('mysql2/promise');
require('dotenv').config();

async function initDatabase() {
  console.log('🚀 开始初始化数据库...');

  try {
    // 创建数据库连接
    const connection = await mysql.createConnection({
      host: process.env.DB_HOST,
      port: process.env.DB_PORT || 3306,
      user: process.env.DB_USER,
      password: process.env.DB_PASSWORD,
      database: process.env.DB_NAME,
      multipleStatements: true
    });

    console.log('✅ 数据库连接成功');

    // 读取SQL文件
    const sqlFile = path.join(__dirname, '../database/init.sql');
    const sqlContent = fs.readFileSync(sqlFile, 'utf8');

    // 分割SQL语句并逐个执行
    const sqlStatements = sqlContent.split(';').filter(stmt => stmt.trim());

    for (let i = 0; i < sqlStatements.length; i++) {
      const statement = sqlStatements[i].trim();
      if (statement) {
        try {
          await connection.execute(statement);
          console.log(`✅ 执行SQL语句 ${i + 1}/${sqlStatements.length}`);
        } catch (error) {
          console.log(`⚠️  SQL语句 ${i + 1} 执行失败（可能是重复创建）: ${error.message}`);
        }
      }
    }

    console.log('✅ 数据库表创建成功');
    console.log('✅ 初始数据插入成功');
    console.log('📝 默认管理员账号：admin / 123456');

    await connection.end();
    console.log('🎉 数据库初始化完成！');

  } catch (error) {
    console.error('❌ 数据库初始化失败:', error.message);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  initDatabase();
}

module.exports = initDatabase;
