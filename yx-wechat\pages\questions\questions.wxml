<!--pages/questions/questions.wxml-->
<view class="container">
  <!-- 进度条 -->
  <view class="progress-bar" wx:if="{{ questions.length > 0 }}">
    <view class="progress-info">
      <text>{{ currentIndex + 1 }} / {{ questions.length }}</text>
      <text>{{ Math.round(((currentIndex + 1) / questions.length) * 100) }}%</text>
    </view>
    <progress 
      percent="{{ ((currentIndex + 1) / questions.length) * 100 }}" 
      stroke-width="3" 
      activeColor="#1989fa"
      backgroundColor="#ebedf0"
    />
  </view>

  <!-- 题目内容 -->
  <view class="question-content" wx:if="{{ currentQuestion }}">
    <view class="question-header">
      <view class="tag tag-primary">{{ getQuestionTypeText(currentQuestion.type) }}</view>
      <view class="tag tag-success" wx:if="{{ currentQuestion.difficulty }}">
        {{ getDifficultyText(currentQuestion.difficulty) }}
      </view>
    </view>

    <view class="question-title">
      {{ currentQuestion.content }}
    </view>

    <!-- 单选题 -->
    <view class="options" wx:if="{{ currentQuestion.type === 'single' }}">
      <radio-group bindchange="onSingleChoiceChange">
        <label class="option-item" wx:for="{{ currentQuestion.options }}" wx:key="index">
          <radio value="{{ item.key }}" checked="{{ selectedAnswer === item.key }}" />
          <text class="option-text">{{ item.key }}. {{ item.value }}</text>
        </label>
      </radio-group>
    </view>

    <!-- 多选题 -->
    <view class="options" wx:elif="{{ currentQuestion.type === 'multiple' }}">
      <checkbox-group bindchange="onMultipleChoiceChange">
        <label class="option-item" wx:for="{{ currentQuestion.options }}" wx:key="index">
          <checkbox value="{{ item.key }}" checked="{{ selectedAnswers.indexOf(item.key) !== -1 }}" />
          <text class="option-text">{{ item.key }}. {{ item.value }}</text>
        </label>
      </checkbox-group>
    </view>

    <!-- 判断题 -->
    <view class="options" wx:elif="{{ currentQuestion.type === 'judge' }}">
      <radio-group bindchange="onSingleChoiceChange">
        <label class="option-item">
          <radio value="true" checked="{{ selectedAnswer === 'true' }}" />
          <text class="option-text">正确</text>
        </label>
        <label class="option-item">
          <radio value="false" checked="{{ selectedAnswer === 'false' }}" />
          <text class="option-text">错误</text>
        </label>
      </radio-group>
    </view>

    <!-- 填空题 -->
    <view class="fill-blank" wx:elif="{{ currentQuestion.type === 'fill' }}">
      <textarea 
        value="{{ fillAnswer }}"
        placeholder="请输入答案"
        bindinput="onFillAnswerChange"
        auto-height
        class="textarea-field"
      />
    </view>

    <!-- 简答题 -->
    <view class="essay" wx:elif="{{ currentQuestion.type === 'essay' }}">
      <view class="textarea-container">
        <textarea 
          value="{{ essayAnswer }}"
          placeholder="请输入答案"
          bindinput="onEssayAnswerChange"
          auto-height
          maxlength="1000"
          class="textarea-field"
        />
        <view class="word-limit">{{ essayAnswer.length }}/1000</view>
      </view>
    </view>

    <!-- 答案解析 -->
    <view class="answer-analysis" wx:if="{{ showAnalysis }}">
      <view class="divider">答案解析</view>
      <view class="correct-answer">
        <text class="label">正确答案：</text>
        <text class="answer">{{ getCorrectAnswerText() }}</text>
      </view>
      <view class="analysis-content" wx:if="{{ currentQuestion.analysis }}">
        <text class="label">解析：</text>
        <text class="content">{{ currentQuestion.analysis }}</text>
      </view>
    </view>
  </view>

  <!-- 操作按钮 -->
  <view class="action-buttons" wx:if="{{ currentQuestion }}">
    <button
      wx:if="{{ !showAnalysis }}"
      type="primary" 
      bindtap="submitAnswer"
      disabled="{{ !hasAnswer() }}"
      loading="{{ submitting }}"
      class="action-button"
    >
      提交答案
    </button>
    
    <view class="button-group" wx:else>
      <button 
        wx:if="{{ currentIndex > 0 }}"
        type="default" 
        bindtap="prevQuestion"
      >
        上一题
      </button>
      <button 
        wx:if="{{ currentIndex < questions.length - 1 }}"
        type="primary" 
        bindtap="nextQuestion"
        class="action-button success-button"
      >
        下一题
      </button>
      <button 
        wx:else
        type="default" 
        bindtap="finishPractice"
        class="action-button warning-button"
      >
        完成练习
      </button>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:elif="{{ !loading }}">
    <view class="icon-placeholder">
      <image src="/images/question-o.png" mode="aspectFit" class="placeholder-icon" />
    </view>
    <view class="empty-text">该章节暂无题目</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{ loading }}">
    <view class="loading-icon"></view>
    <view class="loading-text">加载中...</view>
  </view>
</view>

<!-- 原生组件替代 Vant 组件 -->
