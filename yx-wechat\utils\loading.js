// utils/loading.js
// 替代 Vant Loading 的原生实现

const Loading = {
  // 显示加载提示
  show(options = {}) {
    wx.showLoading({
      title: options.message || '加载中...',
      mask: options.mask !== false // 默认显示遮罩
    })
  },

  // 隐藏加载提示
  hide() {
    wx.hideLoading()
  },

  // 显示带文本的加载提示
  text(message, options = {}) {
    wx.showLoading({
      title: message,
      mask: options.mask !== false
    })
  }
}

module.exports = Loading
