/* components/question-item/question-item.wxss */
.question-item {
  background: #fff;
  border-radius: 16rpx;
  padding: 32rpx;
  margin-bottom: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(100, 101, 102, 0.08);
}

/* 题目头部 */
.question-header {
  display: flex;
  align-items: center;
  gap: 16rpx;
  margin-bottom: 24rpx;
}

/* 标签样式 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 0 12rpx;
  font-size: 20rpx;
  line-height: 32rpx;
  border-radius: 4rpx;
}

.tag-primary {
  color: #1989fa;
  background-color: #ecf5ff;
}

.tag-success {
  color: #07c160;
  background-color: #f0fff0;
}

.question-score {
  margin-left: auto;
  font-size: 24rpx;
  color: #ff976a;
  font-weight: 500;
}

/* 题目内容 */
.question-content {
  font-size: 32rpx;
  line-height: 1.6;
  color: #323233;
  margin-bottom: 32rpx;
}

/* 题目选项 */
.question-options {
  margin-bottom: 32rpx;
}

.option-item {
  display: flex;
  align-items: flex-start;
  padding: 24rpx;
  background: #f7f8fa;
  border-radius: 12rpx;
  margin-bottom: 16rpx;
  border: 2rpx solid transparent;
  transition: all 0.2s;
}

.option-item:last-child {
  margin-bottom: 0;
}

.option-item.selected {
  background: #e8f3ff;
  border-color: #1989fa;
}

.option-item:active {
  background: #e8f3ff;
}

.option-label {
  width: 48rpx;
  height: 48rpx;
  background: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #646566;
  margin-right: 24rpx;
  flex-shrink: 0;
}

.option-item.selected .option-label {
  background: #1989fa;
  color: #fff;
}

.option-content {
  flex: 1;
  font-size: 28rpx;
  line-height: 1.5;
  color: #323233;
  padding-top: 10rpx;
}

/* 答案解析 */
.question-analysis {
  margin-top: 32rpx;
  padding-top: 32rpx;
}

/* 分割线样式 */
.divider {
  display: flex;
  align-items: center;
  margin: 32rpx 0;
  color: #969799;
  font-size: 28rpx;
}

.divider-line {
  flex: 1;
  height: 1px;
  background-color: #ebedf0;
}

.divider-text {
  padding: 0 32rpx;
  color: #969799;
}

.correct-answer,
.analysis-content {
  margin-bottom: 24rpx;
  line-height: 1.6;
}

.correct-answer:last-child,
.analysis-content:last-child {
  margin-bottom: 0;
}

.label {
  font-weight: 600;
  color: #323233;
  font-size: 28rpx;
}

.answer {
  color: #07c160;
  font-weight: 500;
  font-size: 28rpx;
}

.content {
  color: #646566;
  font-size: 28rpx;
}
