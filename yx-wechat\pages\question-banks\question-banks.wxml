<!--pages/question-banks/question-banks.wxml-->
<view class="container">
  <!-- 题库列表 -->
  <view class="banks-list" wx:if="{{ questionBanks.length > 0 }}">
    <view 
      class="bank-item" 
      wx:for="{{ questionBanks }}" 
      wx:key="id"
      bind:tap="goToBooks"
      data-bank="{{ item }}"
    >
      <view class="bank-info">
        <view class="bank-name">{{ item.name }}</view>
        <view class="bank-desc">{{ item.description || '暂无描述' }}</view>
        <view class="bank-stats">
          <view class="tag tag-primary">{{ item.bookCount || 0 }}本书籍</view>
          <view class="tag tag-success">{{ item.questionCount || 0 }}道题目</view>
        </view>
      </view>
      <image src="/images/arrow.png" class="icon-arrow" />
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:elif="{{ !loading }}">
    <image src="/images/notes-o.png" class="empty-icon" />
    <view class="empty-text">该学科暂无题库</view>
  </view>

  <!-- 加载状态 -->
  <view class="loading-state" wx:if="{{ loading }}">
    <view class="loading-spinner"></view>
    <view class="loading-text">加载中...</view>
  </view>
</view>
