const Router = require('koa-router');
const { response } = require('../utils/response');
const { query, transaction } = require('../config/database');
const { authMiddleware, permissionMiddleware } = require('../middleware/auth');
const { validation } = require('../utils/validation');

const router = new Router();

// 应用认证中间件
router.use(authMiddleware);

// 获取菜单列表
router.get('/', permissionMiddleware('system:menu'), async (ctx) => {
  const menus = await query(
    'SELECT id, name, path, component, icon, parent_id, sort, type, permission, status, created_time, updated_time FROM menus ORDER BY sort ASC, created_time DESC'
  );

  // 构建树形结构
  const menuTree = buildMenuTree(menus);
  ctx.body = response.success(menuTree);
});

// 获取用户菜单（根据权限过滤）
router.get('/user', async (ctx) => {
  const userId = ctx.state.user.id;

  // 查询用户可访问的菜单
  const menus = await query(`
    SELECT DISTINCT m.id, m.name, m.path, m.component, m.icon, m.parent_id, m.sort, m.type, m.permission
    FROM menus m
    LEFT JOIN permissions p ON m.permission = p.code
    LEFT JOIN role_permissions rp ON p.id = rp.permission_id
    LEFT JOIN user_roles ur ON rp.role_id = ur.role_id
    WHERE (ur.user_id = ? OR m.permission IS NULL OR m.permission = '')
      AND m.status = 1
      AND m.type = 'menu'
    ORDER BY m.sort ASC, m.created_time DESC
  `, [userId]);

  // 构建树形结构
  const menuTree = buildMenuTree(menus);
  ctx.body = response.success(menuTree);
});

// 获取所有启用的菜单（不分页）
router.get('/all', permissionMiddleware('system:menu'), async (ctx) => {
  const menus = await query(
    'SELECT id, name, path, component, icon, parent_id, sort, type, permission FROM menus WHERE status = 1 ORDER BY sort ASC, created_time DESC'
  );

  ctx.body = response.success(menus);
});

// 创建菜单
router.post('/', permissionMiddleware('system:menu'), async (ctx) => {
  const { name, path, component, icon, parentId = 0, sort = 0, type = 'menu', permission, status = 1 } = ctx.request.body;

  // 参数验证
  validation.required(name, '菜单名称');
  validation.length(name, 1, 50, '菜单名称');

  if (type === 'menu') {
    validation.required(path, '菜单路径');
  }

  // 创建菜单
  const result = await query(
    'INSERT INTO menus (name, path, component, icon, parent_id, sort, type, permission, status, created_time) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)',
    [name, path, component, icon, parentId, sort, type, permission, status, new Date()]
  );

  ctx.body = response.success({ id: result.insertId }, '菜单创建成功');
});

// 更新菜单
router.put('/:id', permissionMiddleware('system:menu'), async (ctx) => {
  const { id } = ctx.params;
  const { name, path, component, icon, parentId, sort, type, permission, status } = ctx.request.body;

  // 参数验证
  validation.required(name, '菜单名称');
  validation.length(name, 1, 50, '菜单名称');

  // 检查菜单是否存在
  const existingMenus = await query('SELECT id FROM menus WHERE id = ?', [id]);
  if (existingMenus.length === 0) {
    ctx.status = 404;
    ctx.body = response.error('菜单不存在', 404);
    return;
  }

  // 更新菜单
  await query(
    'UPDATE menus SET name = ?, path = ?, component = ?, icon = ?, parent_id = ?, sort = ?, type = ?, permission = ?, status = ?, updated_time = ? WHERE id = ?',
    [name, path, component, icon, parentId, sort, type, permission, status, new Date(), id]
  );

  ctx.body = response.success(null, '菜单更新成功');
});

// 删除菜单
router.delete('/:id', permissionMiddleware('system:menu'), async (ctx) => {
  const { id } = ctx.params;

  // 检查是否有子菜单
  const childMenus = await query('SELECT id FROM menus WHERE parent_id = ?', [id]);
  if (childMenus.length > 0) {
    ctx.status = 400;
    ctx.body = response.error('存在子菜单，无法删除', 400);
    return;
  }

  // 删除菜单
  await query('DELETE FROM menus WHERE id = ?', [id]);

  ctx.body = response.success(null, '菜单删除成功');
});

// 构建菜单树形结构
function buildMenuTree(menus, parentId = 0) {
  const tree = [];

  for (const menu of menus) {
    if (menu.parent_id === parentId) {
      const children = buildMenuTree(menus, menu.id);
      if (children.length > 0) {
        menu.children = children;
      }
      tree.push(menu);
    }
  }

  return tree;
}

module.exports = router;
