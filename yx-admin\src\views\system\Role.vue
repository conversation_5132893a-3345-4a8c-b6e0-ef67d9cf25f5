<template>
  <div class="role-management">
    <el-card>
      <!-- 搜索区域 -->
      <div class="search-area">
        <el-form :inline="true" :model="searchForm" class="search-form">
          <el-form-item label="角色名称">
            <el-input v-model="searchForm.name" placeholder="请输入角色名称" clearable></el-input>
          </el-form-item>
          <el-form-item label="状态">
            <el-select v-model="searchForm.status" placeholder="请选择状态" clearable>
              <el-option label="启用" value="1"></el-option>
              <el-option label="禁用" value="0"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch">搜索</el-button>
            <el-button @click="handleReset">重置</el-button>
            <el-button type="success" @click="handleAdd" v-permission="'system:role:add'">新增角色</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- 表格区域 -->
      <el-table :data="tableData" style="width: 100%" v-loading="loading">
        <el-table-column prop="id" label="ID" width="80"></el-table-column>
        <el-table-column prop="name" label="角色名称"></el-table-column>
        <el-table-column prop="code" label="角色编码"></el-table-column>
        <el-table-column prop="description" label="描述"></el-table-column>
        <el-table-column prop="status" label="状态">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === 1 ? 'success' : 'danger'">
              {{ scope.row.status === 1 ? '启用' : '禁用' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间"></el-table-column>
        <el-table-column label="操作" width="250">
          <template slot-scope="scope">
            <el-button size="mini" @click="handleEdit(scope.row)" v-permission="'system:role:edit'">编辑</el-button>
            <el-button size="mini" type="warning" @click="handlePermission(scope.row)" v-permission="'system:role:permission'">权限</el-button>
            <el-button size="mini" type="danger" @click="handleDelete(scope.row)" v-permission="'system:role:delete'">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <el-pagination
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
        :current-page="pagination.currentPage"
        :page-sizes="[10, 20, 50, 100]"
        :page-size="pagination.pageSize"
        layout="total, sizes, prev, pager, next, jumper"
        :total="pagination.total"
        style="margin-top: 20px; text-align: right;">
      </el-pagination>
    </el-card>

    <!-- 新增/编辑对话框 -->
    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="600px">
      <el-form :model="form" :rules="rules" ref="form" label-width="80px">
        <el-form-item label="角色名称" prop="name">
          <el-input v-model="form.name"></el-input>
        </el-form-item>
        <el-form-item label="角色编码" prop="code">
          <el-input v-model="form.code" :disabled="isEdit"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="description">
          <el-input v-model="form.description" type="textarea" rows="3"></el-input>
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio :label="1">启用</el-radio>
            <el-radio :label="0">禁用</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handleSubmit">确定</el-button>
      </div>
    </el-dialog>

    <!-- 权限分配对话框 -->
    <el-dialog title="权限分配" :visible.sync="permissionDialogVisible" width="600px">
      <el-tree
        :data="permissionTree"
        :props="treeProps"
        show-checkbox
        node-key="id"
        ref="permissionTree"
        :default-checked-keys="checkedPermissions">
      </el-tree>
      <div slot="footer" class="dialog-footer">
        <el-button @click="permissionDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="handlePermissionSubmit">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { api } from '@/api'
import permissionMixin from '@/mixins/permission'

export default {
  name: 'Role',
  mixins: [permissionMixin],
  data() {
    return {
      loading: false,
      searchForm: {
        name: '',
        status: ''
      },
      tableData: [],
      pagination: {
        currentPage: 1,
        pageSize: 10,
        total: 0
      },
      dialogVisible: false,
      permissionDialogVisible: false,
      isEdit: false,
      currentRole: null,
      form: {
        id: null,
        name: '',
        code: '',
        description: '',
        status: 1
      },
      rules: {
        name: [
          { required: true, message: '请输入角色名称', trigger: 'blur' }
        ],
        code: [
          { required: true, message: '请输入角色编码', trigger: 'blur' }
        ]
      },
      permissionTree: [
        {
          id: 1,
          label: '系统管理',
          children: [
            { id: 11, label: '用户管理' },
            { id: 12, label: '角色管理' },
            { id: 13, label: '菜单管理' },
            { id: 14, label: '权限管理' },
            { id: 15, label: '系统日志' }
          ]
        },
        {
          id: 2,
          label: '内容管理',
          children: [
            { id: 21, label: '题目管理' },
            { id: 22, label: '分类管理' }
          ]
        }
      ],
      treeProps: {
        children: 'children',
        label: 'name'
      },
      checkedPermissions: []
    }
  },
  computed: {
    dialogTitle() {
      return this.isEdit ? '编辑角色' : '新增角色'
    }
  },
  methods: {
    async handleSearch() {
      this.pagination.currentPage = 1
      await this.loadData()
    },
    handleReset() {
      this.searchForm = {
        name: '',
        status: ''
      }
      this.loadData()
    },
    handleAdd() {
      this.isEdit = false
      this.form = {
        id: null,
        name: '',
        code: '',
        description: '',
        status: 1
      }
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.isEdit = true
      this.form = { ...row }
      this.dialogVisible = true
    },
    async handlePermission(row) {
      this.currentRole = row
      try {
        // 获取角色详情和权限
        const response = await api.roles.permissions(row.id)
        const permissions = response.data || []
        this.checkedPermissions = permissions.map(p => p.id)
        this.permissionDialogVisible = true
      } catch (error) {
        this.$message.error('获取角色权限失败')
      }
    },
    async handleDelete(row) {
      try {
        await this.$confirm('确定要删除该角色吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        await api.roles.delete(row.id)
        this.$message.success('删除成功')
        await this.loadData()
      } catch (error) {
        if (error !== 'cancel') {
          this.$message.error('删除失败')
        }
      }
    },
    async handleSubmit() {
      try {
        const valid = await this.$refs.form.validate()
        if (!valid) return

        if (this.isEdit) {
          await api.roles.update(this.form.id, this.form)
          this.$message.success('编辑成功')
        } else {
          await api.roles.create(this.form)
          this.$message.success('新增成功')
        }

        this.dialogVisible = false
        await this.loadData()
      } catch (error) {
        this.$message.error(this.isEdit ? '编辑失败' : '新增失败')
      }
    },
    async handlePermissionSubmit() {
      try {
        const checkedKeys = this.$refs.permissionTree.getCheckedKeys()
        await api.roles.assignPermissions(this.currentRole.id, { permissionIds: checkedKeys })
        this.$message.success('权限分配成功')
        this.permissionDialogVisible = false
      } catch (error) {
        this.$message.error('权限分配失败')
      }
    },
    async handleSizeChange(val) {
      this.pagination.pageSize = val
      await this.loadData()
    },
    async handleCurrentChange(val) {
      this.pagination.currentPage = val
      await this.loadData()
    },
    async loadData() {
      try {
        this.loading = true
        const params = {
          page: this.pagination.currentPage,
          pageSize: this.pagination.pageSize,
          ...this.searchForm
        }

        const response = await api.roles.list(params)
        this.tableData = response.data.list || []
        this.pagination.total = response.data.pagination.total || 0
      } catch (error) {
        this.$message.error('加载数据失败')
      } finally {
        this.loading = false
      }
    },
    async loadPermissions() {
      try {
        const response = await api.permissions.list()
        // 构建权限树
        this.permissionTree = this.buildPermissionTree(response.data || [])
      } catch (error) {
        console.error('加载权限失败:', error)
      }
    },
    buildPermissionTree(permissions) {
      const tree = []
      const map = {}

      // 创建映射
      permissions.forEach(permission => {
        map[permission.id] = { ...permission, children: [] }
      })

      // 构建树形结构
      permissions.forEach(permission => {
        if (permission.parent_id === 0) {
          tree.push(map[permission.id])
        } else if (map[permission.parent_id]) {
          map[permission.parent_id].children.push(map[permission.id])
        }
      })

      return tree
    }
  },
  async mounted() {
    await this.loadData()
    await this.loadPermissions()
  }
}
</script>

<style lang="scss" scoped>
.role-management {
  .search-area {
    margin-bottom: 20px;

    .search-form {
      .el-form-item {
        margin-bottom: 0;
      }
    }
  }
}
</style>
