// 验证工具函数
const validation = {
  // 验证邮箱格式
  isEmail: (email) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },
  
  // 验证手机号格式
  isPhone: (phone) => {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },
  
  // 验证密码强度（至少6位，包含字母和数字）
  isStrongPassword: (password) => {
    const passwordRegex = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d@$!%*#?&]{6,}$/;
    return passwordRegex.test(password);
  },
  
  // 验证用户名格式（3-20位字母数字下划线）
  isUsername: (username) => {
    const usernameRegex = /^[a-zA-Z0-9_]{3,20}$/;
    return usernameRegex.test(username);
  },
  
  // 验证必填字段
  required: (value, fieldName) => {
    if (value === undefined || value === null || value === '') {
      throw new Error(`${fieldName}不能为空`);
    }
    return true;
  },
  
  // 验证字符串长度
  length: (value, min, max, fieldName) => {
    if (typeof value !== 'string') {
      throw new Error(`${fieldName}必须是字符串`);
    }
    if (value.length < min || value.length > max) {
      throw new Error(`${fieldName}长度必须在${min}-${max}之间`);
    }
    return true;
  },
  
  // 验证数字范围
  range: (value, min, max, fieldName) => {
    const num = Number(value);
    if (isNaN(num)) {
      throw new Error(`${fieldName}必须是数字`);
    }
    if (num < min || num > max) {
      throw new Error(`${fieldName}必须在${min}-${max}之间`);
    }
    return true;
  },
  
  // 验证数组
  isArray: (value, fieldName) => {
    if (!Array.isArray(value)) {
      throw new Error(`${fieldName}必须是数组`);
    }
    return true;
  }
};

module.exports = {
  validation
};
